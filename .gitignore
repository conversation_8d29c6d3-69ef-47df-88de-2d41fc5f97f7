# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Go dependencies and build files
go-project-server/vendor/
go-project-server/*.exe
go-project-server/*.dll
go-project-server/*.so
go-project-server/*.dylib
go-project-server/go.sum

# Build outputs
go-project/dist/
go-project/dist-ssr/
go-project/.vite/
go-project-server/tmp/

# Environment files
.env
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp

# Configuration files
claude_desktop_config.json
mcp.json