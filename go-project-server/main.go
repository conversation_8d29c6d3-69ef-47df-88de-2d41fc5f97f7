// @title Go项目API文档
// @version 1.0
// @description 这是一个基本的CRUD API服务
// @termsOfService http://swagger.io/terms/

// @contact.name API支持
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /
package main

import (
    "go-project/config"
    "go-project/database"
    "go-project/routes"
    "go-project/utils"
    "log"
    
    // 添加这行来导入docs包，初始化Swagger文档
    _ "go-project/docs"
)

func main() {
    // 设置日志
    utils.SetupLogger()
    
    // 加载配置
    cfg := config.LoadConfig()
    utils.LogInfo("配置加载完成")
    
    // 初始化数据库
    if err := database.InitDatabase(cfg); err != nil {
        log.Fatal("数据库初始化失败:", err)
    }
    
    // 设置路由
    r := routes.SetupRoutes()
    
    // 启动服务器
    utils.LogInfo("服务器启动在 http://localhost:" + cfg.Port)
    utils.LogInfo("Swagger文档地址: http://localhost:" + cfg.Port + "/swagger/index.html")
    
    if err := r.Run(":" + cfg.Port); err != nil {
        log.Fatal("服务器启动失败:", err)
    }
}