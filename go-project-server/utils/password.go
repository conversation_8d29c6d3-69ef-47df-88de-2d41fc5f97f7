package utils

import (
	"errors"
	"time"
	"unicode"
	"golang.org/x/crypto/bcrypt"
)

// PasswordPolicy 密码策略
type PasswordPolicy struct {
	MinLength    int
	MaxLength    int
	RequireUpper bool
	RequireLower bool
	RequireDigit bool
	RequireSpecial bool
	ExpiryDays   int
}

// DefaultPasswordPolicy 默认密码策略
var DefaultPasswordPolicy = PasswordPolicy{
	MinLength:    8,
	MaxLength:    128,
	RequireUpper: true,
	RequireLower: true,
	RequireDigit: true,
	RequireSpecial: true,
	ExpiryDays:   90,
}

// ValidatePassword 验证密码是否符合策略
func ValidatePassword(password string, policy PasswordPolicy) error {
	if len(password) < policy.MinLength {
		return errors.New("密码长度不能少于" + string(rune(policy.MinLength)) + "位")
	}
	
	if len(password) > policy.MaxLength {
		return errors.New("密码长度不能超过" + string(rune(policy.MaxLength)) + "位")
	}
	
	var hasUpper, hasLower, hasDigit, hasSpecial bool
	
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}
	
	if policy.RequireUpper && !hasUpper {
		return errors.New("密码必须包含大写字母")
	}
	
	if policy.RequireLower && !hasLower {
		return errors.New("密码必须包含小写字母")
	}
	
	if policy.RequireDigit && !hasDigit {
		return errors.New("密码必须包含数字")
	}
	
	if policy.RequireSpecial && !hasSpecial {
		return errors.New("密码必须包含特殊字符")
	}
	
	return nil
}

// IsPasswordExpired 检查密码是否过期
func IsPasswordExpired(lastChanged time.Time, policy PasswordPolicy) bool {
	if policy.ExpiryDays <= 0 {
		return false
	}
	
	expiryDate := lastChanged.AddDate(0, 0, policy.ExpiryDays)
	return time.Now().After(expiryDate)
}

// IsCommonPassword 检查是否为常见密码
func IsCommonPassword(password string) bool {
	commonPasswords := []string{
		"123456", "password", "123456789", "12345678", "12345",
		"1234567", "1234567890", "qwerty", "abc123", "111111",
		"123123", "admin", "letmein", "welcome", "monkey",
		"password123", "123qwe", "qwerty123", "000000", "666666",
	}
	
	for _, common := range commonPasswords {
		if password == common {
			return true
		}
	}
	return false
}

// HashPassword 生成密码哈希
func HashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

// GeneratePasswordHash 生成密码哈希
func GeneratePasswordHash(password string) (string, error) {
	// 验证密码策略
	if err := ValidatePassword(password, DefaultPasswordPolicy); err != nil {
		return "", err
	}
	
	// 检查常见密码
	if IsCommonPassword(password) {
		return "", errors.New("不能使用常见密码")
	}
	
	// 使用bcrypt加密
	return HashPassword(password)
}