basePath: /
definitions:
  controllers.PagedResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
      page:
        type: integer
      size:
        type: integer
      total:
        type: integer
    type: object
  controllers.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  models.CreateUserRequest:
    properties:
      age:
        example: 25
        maximum: 120
        minimum: 1
        type: integer
      email:
        example: <EMAIL>
        type: string
      name:
        example: 张三
        type: string
      phone:
        example: "13800138000"
        type: string
    required:
    - email
    - name
    type: object
  models.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: "123456"
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  models.LoginResponse:
    properties:
      token:
        type: string
      user:
        $ref: '#/definitions/models.UserResponse'
    type: object
  models.RegisterRequest:
    properties:
      age:
        example: 25
        maximum: 120
        minimum: 1
        type: integer
      email:
        example: <EMAIL>
        type: string
      name:
        example: 张三
        type: string
      password:
        example: "123456"
        minLength: 6
        type: string
      phone:
        example: "13800138000"
        type: string
    required:
    - email
    - name
    - password
    type: object
  models.UpdateUserRequest:
    properties:
      age:
        example: 30
        type: integer
      email:
        example: <EMAIL>
        type: string
      name:
        example: 李四
        type: string
      phone:
        example: "13900139000"
        type: string
    type: object
  models.UserResponse:
    properties:
      age:
        example: 25
        type: integer
      created_at:
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: 1
        type: integer
      name:
        example: 张三
        type: string
      phone:
        example: "13800138000"
        type: string
      updated_at:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API支持
    url: http://www.swagger.io/support
  description: 这是一个基本的CRUD API服务
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Go项目API文档
  version: "1.0"
paths:
  /api/auth/login:
    post:
      consumes:
      - application/json
      description: 用户登录接口
      parameters:
      - description: 登录信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/controllers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.LoginResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/controllers.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 用户登录
      tags:
      - 认证
  /api/auth/register:
    post:
      consumes:
      - application/json
      description: 用户注册接口
      parameters:
      - description: 注册信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/controllers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/controllers.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 用户注册
      tags:
      - 用户管理
  /api/users:
    get:
      consumes:
      - application/json
      description: 分页获取用户列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/controllers.PagedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.UserResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 获取用户列表
      tags:
      - 用户管理
    post:
      consumes:
      - application/json
      description: 创建新用户
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/controllers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 创建用户
      tags:
      - 用户管理
  /api/users/{id}:
    delete:
      consumes:
      - application/json
      description: 根据用户ID删除用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 删除用户
      tags:
      - 用户管理
    get:
      consumes:
      - application/json
      description: 根据用户ID获取用户信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/controllers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 获取用户信息
      tags:
      - 用户管理
    put:
      consumes:
      - application/json
      description: 根据用户ID更新用户信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新的用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/controllers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/controllers.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/controllers.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/controllers.Response'
      summary: 更新用户信息
      tags:
      - 用户管理
swagger: "2.0"
