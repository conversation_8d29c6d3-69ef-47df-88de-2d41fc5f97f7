.PHONY: help build run clean test swagger deps db-init

# 默认目标
help:
	@echo "可用命令:"
	@echo "  deps     - 安装依赖"
	@echo "  swagger  - 生成Swagger文档"
	@echo "  build    - 编译项目"
	@echo "  run      - 运行项目"
	@echo "  db-init  - 初始化数据库"
	@echo "  clean    - 清理编译文件"
	@echo "  test     - 运行测试"

# 安装依赖
deps:
	go mod tidy
	go mod download

# 生成Swagger文档
swagger:
	swag init -g main.go

# 编译项目
build:
	go build -o bin/app.exe main.go

# 运行项目
run:
	go run main.go

# 初始化数据库
db-init:
	mysql -h localhost -P 3306 -u root -p12345678 < sql/init.sql

# 清理编译文件
clean:
	if exist bin rmdir /s /q bin
	if exist docs rmdir /s /q docs

# 运行测试
test:
	go test ./...

# 开发模式运行（自动重载）
dev:
	go run main.go

# 格式化代码
fmt:
	go fmt ./...

# 检查代码
vet:
	go vet ./...

# 下载依赖
mod-download:
	go mod download

# 整理依赖
mod-tidy:
	go mod tidy