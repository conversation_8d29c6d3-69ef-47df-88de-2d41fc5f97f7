package controllers

import (
	"go-project/models"
	"go-project/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	userService *services.UserService
}

func NewUserController() *UserController {
	return &UserController{
		userService: services.NewUserService(),
	}
}

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type PagedResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body models.CreateUserRequest true "用户信息"
// @Success 201 {object} Response{data=models.UserResponse}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/users [post]
func (c *UserController) CreateUser(ctx *gin.Context) {
	var req models.CreateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	user, err := c.userService.CreateUser(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "创建用户失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, Response{
		Code:    201,
		Message: "创建成功",
		Data:    user,
	})
}

// GetUser 获取用户
// @Summary 获取用户信息
// @Description 根据用户ID获取用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} Response{data=models.UserResponse}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /api/users/{id} [get]
func (c *UserController) GetUser(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的用户ID",
		})
		return
	}

	user, err := c.userService.GetUserByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, Response{
			Code:    404,
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取成功",
		Data:    user,
	})
}

// GetUsers 获取用户列表
// @Summary 获取用户列表
// @Description 分页获取用户列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Success 200 {object} PagedResponse{data=[]models.UserResponse}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/users [get]
func (c *UserController) GetUsers(ctx *gin.Context) {
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	users, total, err := c.userService.GetAllUsers(page, size)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取用户列表失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, PagedResponse{
		Code:    200,
		Message: "获取成功",
		Data:    users,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// UpdateUser 更新用户
// @Summary 更新用户信息
// @Description 根据用户ID更新用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param user body models.UpdateUserRequest true "更新的用户信息"
// @Success 200 {object} Response{data=models.UserResponse}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/users/{id} [put]
func (c *UserController) UpdateUser(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的用户ID",
		})
		return
	}

	var req models.UpdateUserRequest
	if bindErr := ctx.ShouldBindJSON(&req); bindErr != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + bindErr.Error(),
		})
		return
	}

	user, err := c.userService.UpdateUser(uint(id), &req)
	if err != nil {
		if err.Error() == "用户不存在" {
			ctx.JSON(http.StatusNotFound, Response{
				Code:    404,
				Message: err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, Response{
				Code:    500,
				Message: "更新用户失败: " + err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "更新成功",
		Data:    user,
	})
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 根据用户ID删除用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/users/{id} [delete]
func (c *UserController) DeleteUser(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的用户ID",
		})
		return
	}

	err = c.userService.DeleteUser(uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "删除用户失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "删除成功",
	})
}

// RegisterUser 用户注册
// @Summary 用户注册
// @Description 用户注册接口
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body models.RegisterRequest true "注册信息"
// @Success 201 {object} Response{data=models.UserResponse}
// @Failure 400 {object} Response
// @Failure 409 {object} Response
// @Failure 500 {object} Response
// @Router /api/auth/register [post]
func (c *UserController) RegisterUser(ctx *gin.Context) {
	var req models.RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	user, err := c.userService.RegisterUser(&req)
	if err != nil {
		if err.Error() == "邮箱已被注册" {
			ctx.JSON(http.StatusConflict, Response{
				Code:    409,
				Message: err.Error(),
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "注册失败: " + err.Error(),
		})
		return
	}

	// 转换为响应格式（不包含密码）
	userResponse := models.UserResponse{
		ID:        user.ID,
		Name:      user.Name,
		Email:     user.Email,
		Age:       user.Age,
		Phone:     user.Phone,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}

	ctx.JSON(http.StatusCreated, Response{
		Code:    201,
		Message: "注册成功",
		Data:    userResponse,
	})
}

// 在现有代码基础上添加以下方法

// LoginUser 用户登录
// @Summary 用户登录
// @Description 用户登录接口
// @Tags 认证
// @Accept json
// @Produce json
// @Param user body models.LoginRequest true "登录信息"
// @Success 200 {object} Response{data=models.LoginResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/auth/login [post]
func (c *UserController) LoginUser(ctx *gin.Context) {
	var req models.LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	response, err := c.userService.LoginUser(&req)
	if err != nil {
		if err.Error() == "邮箱或密码错误" {
			ctx.JSON(http.StatusUnauthorized, Response{
				Code:    401,
				Message: err.Error(),
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "登录失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "登录成功",
		Data:    response,
	})
}
