package controllers

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"go-project/models"
	"go-project/services"
	"github.com/gin-gonic/gin"
)

type UploadController struct {
	fileService *services.FileService
}

func NewUploadController(fileService *services.FileService) *UploadController {
	return &UploadController{
		fileService: fileService,
	}
}

type UploadResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type FileInfo struct {
	FileName string `json:"fileName"`
	FileSize int64  `json:"fileSize"`
	FileURL  string `json:"fileUrl"`
	UploadAt string `json:"uploadAt"`
}

type ChunkInfo struct {
	ChunkIndex int    `json:"chunkIndex"`
	TotalChunks int   `json:"totalChunks"`
	FileName   string `json:"fileName"`
	FileHash   string `json:"fileHash"`
}

const (
	MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB，超过此大小使用分片上传
	CHUNK_SIZE    = 2 * 1024 * 1024   // 2MB 每个分片大小
	UPLOAD_DIR    = "./uploads"
	CHUNK_DIR     = "./uploads/chunks"
)

// @Summary 普通文件上传
// @Description 上传单个文件（小于100MB）
// @Tags 文件上传
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件"
// @Success 200 {object} UploadResponse
// @Router /api/upload/single [post]
// SingleUpload 普通文件上传（更新版本）
func (uc *UploadController) SingleUpload(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, UploadResponse{
			Code:    401,
			Message: "未授权访问",
		})
		return
	}
	
	// 确保上传目录存在
	if err := os.MkdirAll(UPLOAD_DIR, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "创建上传目录失败",
		})
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Code:    400,
			Message: "获取文件失败: " + err.Error(),
		})
		return
	}
	defer file.Close()

	// 检查文件大小
	if header.Size > MAX_FILE_SIZE {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Code:    400,
			Message: fmt.Sprintf("文件过大，请使用分片上传。文件大小: %d MB，最大允许: %d MB", header.Size/(1024*1024), MAX_FILE_SIZE/(1024*1024)),
		})
		return
	}

	// 生成唯一文件名
	fileName := generateFileName(header.Filename)
	filePath := filepath.Join(UPLOAD_DIR, fileName)

	// 保存文件
	out, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "创建文件失败: " + err.Error(),
		})
		return
	}
	defer out.Close()

	// 计算文件哈希
	file.Seek(0, 0) // 重置文件指针
	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "计算文件哈希失败: " + err.Error(),
		})
		return
	}
	fileHash := fmt.Sprintf("%x", hash.Sum(nil))
	
	// 重置文件指针并保存文件
	file.Seek(0, 0)
	_, err = io.Copy(out, file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "保存文件失败: " + err.Error(),
		})
		return
	}
	
	// 获取文件类型
	fileType := filepath.Ext(header.Filename)
	mimeType := mime.TypeByExtension(fileType)
	if mimeType == "" {
		mimeType = "application/octet-stream"
	}

	// 保存文件信息到数据库
	fileModel := &models.File{
		UserID:       userID.(uint),
		OriginalName: header.Filename,
		FileName:     fileName,
		FilePath:     filePath,
		FileSize:     header.Size,
		FileType:     fileType,
		MimeType:     mimeType,
		FileHash:     fileHash,
		UploadType:   "single",
		Status:       "completed",
	}
	
	if err := uc.fileService.CreateFile(fileModel); err != nil {
		// 如果数据库保存失败，删除已上传的文件
		os.Remove(filePath)
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "保存文件信息失败: " + err.Error(),
		})
		return
	}
	
	// 记录上传日志
	log := &models.FileUploadLog{
		FileID:    fileModel.ID,
		UserID:    userID.(uint),
		Action:    "upload",
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
	}
	uc.fileService.LogFileAction(log)

	fileInfo := FileInfo{
		FileName: fileName,
		FileSize: header.Size,
		FileURL:  "/api/upload/file/" + fileName,
		UploadAt: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusOK, UploadResponse{
		Code:    200,
		Message: "文件上传成功",
		Data:    fileInfo,
	})
}

// GetUserFiles 获取用户文件列表
func (uc *UploadController) GetUserFiles(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, UploadResponse{
			Code:    401,
			Message: "未授权访问",
		})
		return
	}
	
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	
	files, total, err := uc.fileService.GetUserFiles(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "获取文件列表失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, UploadResponse{
		Code:    200,
		Message: "获取成功",
		Data: map[string]interface{}{
			"files": files,
			"total": total,
			"page":  page,
			"pageSize": pageSize,
		},
	})
}

// GetFileStats 获取文件统计
func (uc *UploadController) GetFileStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, UploadResponse{
			Code:    401,
			Message: "未授权访问",
		})
		return
	}
	
	stats, err := uc.fileService.GetFileStats(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "获取统计信息失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, UploadResponse{
		Code:    200,
		Message: "获取成功",
		Data:    stats,
	})
}

// @Summary 分片上传
// @Description 上传文件分片
// @Tags 文件上传
// @Accept multipart/form-data
// @Produce json
// @Param chunk formData file true "文件分片"
// @Param chunkIndex formData int true "分片索引"
// @Param totalChunks formData int true "总分片数"
// @Param fileName formData string true "文件名"
// @Param fileHash formData string true "文件哈希"
// @Success 200 {object} UploadResponse
// @Router /api/upload/chunk [post]
func (uc *UploadController) ChunkUpload(c *gin.Context) {
	// 确保分片目录存在
	if err := os.MkdirAll(CHUNK_DIR, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "创建分片目录失败",
		})
		return
	}

	// 获取分片文件
	chunk, _, err := c.Request.FormFile("chunk")
	if err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Code:    400,
			Message: "获取分片失败: " + err.Error(),
		})
		return
	}
	defer chunk.Close()

	// 获取分片信息
	chunkIndex, _ := strconv.Atoi(c.PostForm("chunkIndex"))
	totalChunks, _ := strconv.Atoi(c.PostForm("totalChunks"))
	fileName := c.PostForm("fileName")
	fileHash := c.PostForm("fileHash")

	if fileName == "" || fileHash == "" {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Code:    400,
			Message: "缺少必要参数",
		})
		return
	}

	// 保存分片
	chunkFileName := fmt.Sprintf("%s_%d", fileHash, chunkIndex)
	chunkPath := filepath.Join(CHUNK_DIR, chunkFileName)

	out, err := os.Create(chunkPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "创建分片文件失败: " + err.Error(),
		})
		return
	}
	defer out.Close()

	_, err = io.Copy(out, chunk)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "保存分片失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, UploadResponse{
		Code:    200,
		Message: fmt.Sprintf("分片 %d/%d 上传成功", chunkIndex+1, totalChunks),
		Data: ChunkInfo{
			ChunkIndex:  chunkIndex,
			TotalChunks: totalChunks,
			FileName:    fileName,
			FileHash:    fileHash,
		},
	})
}

// @Summary 合并分片
// @Description 合并所有分片为完整文件
// @Tags 文件上传
// @Accept json
// @Produce json
// @Param data body ChunkInfo true "分片信息"
// @Success 200 {object} UploadResponse
// @Router /api/upload/merge [post]
func (uc *UploadController) MergeChunks(c *gin.Context) {
	var chunkInfo ChunkInfo
	if err := c.ShouldBindJSON(&chunkInfo); err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Code:    400,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	// 确保上传目录存在
	if err := os.MkdirAll(UPLOAD_DIR, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "创建上传目录失败",
		})
		return
	}

	// 生成最终文件名
	finalFileName := generateFileName(chunkInfo.FileName)
	finalPath := filepath.Join(UPLOAD_DIR, finalFileName)

	// 创建最终文件
	finalFile, err := os.Create(finalPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Code:    500,
			Message: "创建最终文件失败: " + err.Error(),
		})
		return
	}
	defer finalFile.Close()

	// 合并所有分片
	var totalSize int64
	for i := 0; i < chunkInfo.TotalChunks; i++ {
		chunkFileName := fmt.Sprintf("%s_%d", chunkInfo.FileHash, i)
		chunkPath := filepath.Join(CHUNK_DIR, chunkFileName)

		chunkFile, err := os.Open(chunkPath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Code:    500,
				Message: fmt.Sprintf("打开分片 %d 失败: %s", i, err.Error()),
			})
			return
		}

		size, err := io.Copy(finalFile, chunkFile)
		if err != nil {
			chunkFile.Close()
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Code:    500,
				Message: fmt.Sprintf("合并分片 %d 失败: %s", i, err.Error()),
			})
			return
		}
		totalSize += size
		chunkFile.Close()

		// 删除分片文件
		os.Remove(chunkPath)
	}

	fileInfo := FileInfo{
		FileName: finalFileName,
		FileSize: totalSize,
		FileURL:  "/api/upload/file/" + finalFileName,
		UploadAt: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusOK, UploadResponse{
		Code:    200,
		Message: "文件合并成功",
		Data:    fileInfo,
	})
}

// @Summary 下载文件
// @Description 下载已上传的文件
// @Tags 文件上传
// @Param fileName path string true "文件名"
// @Success 200 {file} file
// @Router /api/upload/file/{fileName} [get]
func (uc *UploadController) DownloadFile(c *gin.Context) {
	fileName := c.Param("fileName")
	filePath := filepath.Join(UPLOAD_DIR, fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, UploadResponse{
			Code:    404,
			Message: "文件不存在",
		})
		return
	}

	c.File(filePath)
}

// 生成唯一文件名
func generateFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)
	timestamp := time.Now().Unix()
	hash := fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s_%d", name, timestamp))))
	return fmt.Sprintf("%s_%s%s", name, hash[:8], ext)
}