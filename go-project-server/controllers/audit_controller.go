package controllers

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"go-project/models"
	"go-project/services"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type AuditController struct {
	auditService *services.AuditService
}

func NewAuditController() *AuditController {
	return &AuditController{
		auditService: services.NewAuditService(),
	}
}

// GetAuditLogs 获取审计日志列表
// @Summary 获取审计日志列表
// @Description 分页获取审计日志列表，支持筛选
// @Tags 日志管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param user_id query int false "用户ID"
// @Param action query string false "操作类型"
// @Param resource query string false "资源类型"
// @Param method query string false "HTTP方法"
// @Param ip_address query string false "IP地址"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Success 200 {object} PagedResponse{data=[]models.AuditLogResponse}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/audit/logs [get]
func (c *AuditController) GetAuditLogs(ctx *gin.Context) {
	// 解析分页参数
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 20
	}

	// 解析筛选参数
	filters := &models.AuditLogFilter{
		UserID:    parseUintParam(ctx.Query("user_id")),
		Action:    ctx.Query("action"),
		Resource:  ctx.Query("resource"),
		Method:    ctx.Query("method"),
		IPAddress: ctx.Query("ip_address"),
	}

	// 解析日期范围
	if startDate := ctx.Query("start_date"); startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &t
		}
	}

	if endDate := ctx.Query("end_date"); endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			// 设置为当天结束时间
			endTime := t.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			filters.EndDate = &endTime
		}
	}

	// 获取日志数据
	logs, total, err := c.auditService.GetAuditLogs(page, size, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取审计日志失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, PagedResponse{
		Code:    200,
		Message: "获取成功",
		Data:    logs,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// GetSecurityEvents 获取安全事件列表
// @Summary 获取安全事件列表
// @Description 分页获取安全事件列表
// @Tags 日志管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param event_type query string false "事件类型"
// @Param severity query string false "严重程度"
// @Success 200 {object} PagedResponse{data=[]models.SecurityEvent}
// @Failure 500 {object} Response
// @Router /api/audit/security-events [get]
func (c *AuditController) GetSecurityEvents(ctx *gin.Context) {
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 20
	}

	filters := &models.SecurityEventFilter{
		EventType: ctx.Query("event_type"),
		Severity:  ctx.Query("severity"),
	}

	events, total, err := c.auditService.GetSecurityEvents(page, size, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取安全事件失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, PagedResponse{
		Code:    200,
		Message: "获取成功",
		Data:    events,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// GetAuditStats 获取审计统计信息
// @Summary 获取审计统计信息
// @Description 获取审计日志的统计信息
// @Tags 日志管理
// @Accept json
// @Produce json
// @Param days query int false "统计天数" default(7)
// @Success 200 {object} Response{data=models.AuditStats}
// @Failure 500 {object} Response
// @Router /api/audit/stats [get]
func (c *AuditController) GetAuditStats(ctx *gin.Context) {
	daysStr := ctx.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 {
		days = 7
	}

	stats, err := c.auditService.GetAuditStats(days)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取统计信息失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取成功",
		Data:    stats,
	})
}

// parseUintParam 解析uint参数
func parseUintParam(param string) *uint {
	if param == "" {
		return nil
	}
	if val, err := strconv.ParseUint(param, 10, 32); err == nil {
		uintVal := uint(val)
		return &uintVal
	}
	return nil
}

// ExportAuditLogs 导出审计日志
// @Summary 导出审计日志
// @Description 导出审计日志为CSV格式
// @Tags 日志管理
// @Accept json
// @Produce application/octet-stream
// @Param user_id query int false "用户ID"
// @Param action query string false "操作类型"
// @Param resource query string false "资源类型"
// @Param method query string false "HTTP方法"
// @Param ip_address query string false "IP地址"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Success 200 {file} file "CSV文件"
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/audit/export [get]
func (c *AuditController) ExportAuditLogs(ctx *gin.Context) {
	// 解析筛选参数
	filters := &models.AuditLogFilter{
		UserID:    parseUintParam(ctx.Query("user_id")),
		Action:    ctx.Query("action"),
		Resource:  ctx.Query("resource"),
		Method:    ctx.Query("method"),
		IPAddress: ctx.Query("ip_address"),
	}

	// 解析日期参数
	if startDateStr := ctx.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filters.StartDate = &startDate
		}
	}

	if endDateStr := ctx.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			// 设置为当天的23:59:59
			endDate = endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			filters.EndDate = &endDate
		}
	}

	// 获取所有符合条件的日志（不分页）
	logs, err := c.auditService.ExportAuditLogs(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "导出失败: " + err.Error(),
		})
		return
	}

	// 生成CSV内容
	csvContent, err := c.generateCSV(logs)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "生成CSV失败: " + err.Error(),
		})
		return
	}

	// 设置响应头
	filename := fmt.Sprintf("audit_logs_%s.csv", time.Now().Format("20060102_150405"))
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	ctx.Header("Content-Length", strconv.Itoa(len(csvContent)))

	// 返回CSV内容
	ctx.Data(http.StatusOK, "application/octet-stream", csvContent)
}

// generateCSV 生成CSV内容
func (c *AuditController) generateCSV(logs []models.AuditLogResponse) ([]byte, error) {
	var buffer bytes.Buffer
	writer := csv.NewWriter(&buffer)

	// 写入CSV头部（BOM for Excel compatibility）
	buffer.WriteString("\xEF\xBB\xBF")

	// 写入表头
	header := []string{
		"ID", "用户ID", "用户名", "操作类型", "资源类型", "资源ID",
		"HTTP方法", "请求路径", "IP地址", "用户代理", "状态码", "执行时间(ms)", "创建时间",
	}
	if err := writer.Write(header); err != nil {
		return nil, err
	}

	// 写入数据行
	for _, log := range logs {
		userID := ""
		if log.UserID != nil {
			userID = strconv.Itoa(int(*log.UserID))
		}

		resourceID := ""
		if log.ResourceID != nil {
			resourceID = strconv.Itoa(int(*log.ResourceID))
		}

		row := []string{
			strconv.Itoa(int(log.ID)),
			userID,
			log.UserName,
			log.Action,
			log.Resource,
			resourceID,
			log.Method,
			log.Path,
			log.IPAddress,
			log.UserAgent,
			strconv.Itoa(log.StatusCode),
			strconv.FormatInt(log.Duration, 10),
			log.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if err := writer.Write(row); err != nil {
			return nil, err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}