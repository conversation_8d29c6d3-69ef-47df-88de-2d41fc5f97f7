package routes

import (
	"go-project/controllers"
	"go-project/database"     // 添加数据库导入
	"go-project/services"     // 添加服务导入
	"go-project/middleware"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// 在SetupRoutes函数中添加审计日志路由
func SetupRoutes() *gin.Engine {
	r := gin.Default()

	// 创建限流器 (每分钟60次请求)
	rateLimiter := middleware.NewRateLimiter(time.Minute, 60)

	// 全局中间件
	r.Use(middleware.SecurityMiddleware())
	r.Use(middleware.XSSProtectionMiddleware())
	r.Use(middleware.SQLInjectionProtectionMiddleware())
	r.Use(rateLimiter.RateLimitMiddleware())
	r.Use(middleware.AuditLogMiddleware())

	// 配置CORS中间件
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// 添加根路径重定向到Swagger文档
	r.GET("/", func(c *gin.Context) {
		c.Redirect(302, "/swagger/index.html")
	})

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "服务运行正常",
		})
	})

	// 初始化控制器（移到函数开始处）
	userController := controllers.NewUserController()
	auditController := controllers.NewAuditController()
	uploadController := controllers.NewUploadController(services.NewFileService(database.GetDB()))

	// API路由组
	api := r.Group("/api")
	{
		// 用户相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/register", userController.RegisterUser)  // 修复：Register -> RegisterUser
			auth.POST("/login", userController.LoginUser)        // 修复：Login -> LoginUser
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// 用户管理
			users := protected.Group("/users")
			{
				users.GET("", userController.GetUsers)
				users.POST("", userController.CreateUser)
				users.GET("/:id", userController.GetUser)
				users.PUT("/:id", userController.UpdateUser)
				users.DELETE("/:id", userController.DeleteUser)
			}

			// 审计日志
			audit := protected.Group("/audit")
			{
				audit.GET("/logs", auditController.GetAuditLogs)
			}

			// 文件上传路由
			protected.POST("/upload/single", uploadController.SingleUpload)
			protected.POST("/upload/chunk", uploadController.ChunkUpload)
			protected.POST("/upload/merge", uploadController.MergeChunks)
			protected.GET("/upload/files", uploadController.GetUserFiles)
			protected.GET("/upload/stats", uploadController.GetFileStats)
			// 注释掉不存在的DeleteFile方法
			// protected.DELETE("/upload/file/:id", uploadController.DeleteFile)
		}

		// 文件下载路由（无需认证）
		api.GET("/upload/file/:fileName", uploadController.DownloadFile)
	}

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	return r
}
