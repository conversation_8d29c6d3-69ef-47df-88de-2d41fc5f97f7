package config

import (
    "os"
    "github.com/joho/godotenv"
)

type Config struct {
    Port     string
    Host     string
    LogLevel string
    Database DatabaseConfig
}

type DatabaseConfig struct {
    Host     string
    Port     string
    Username string
    Password string
    DBName   string
}

func LoadConfig() *Config {
    // 加载.env文件
    godotenv.Load()
    
    return &Config{
        Port:     getEnv("PORT", "8080"),
        Host:     getEnv("HOST", "localhost"),
        LogLevel: getEnv("LOG_LEVEL", "info"),
        Database: DatabaseConfig{
            Host:     getEnv("DB_HOST", "localhost"),
            Port:     getEnv("DB_PORT", "3306"),
            Username: getEnv("DB_USERNAME", "root"),
            Password: getEnv("DB_PASSWORD", "12345678"),
            DBName:   getEnv("DB_NAME", "go_project_db"),
        },
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}