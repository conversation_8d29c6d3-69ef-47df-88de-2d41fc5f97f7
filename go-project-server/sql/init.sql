-- 创建数据库
CREATE DATABASE IF NOT EXISTS go_project_db;
USE go_project_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,  -- 新增密码字段
    age INT,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 创建用户令牌表
CREATE TABLE IF NOT EXISTS user_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at)
);

-- 插入测试数据（密码为 "123456" 的 bcrypt 哈希值）
INSERT INTO users (name, email, password, age, phone) VALUES 
('张三', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjZjcOPk8xVEOkwJBbQbC9H7FOdHNe', 25, '13800138001'),
('李四', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjZjcOPk8xVEOkwJBbQbC9H7FOdHNe', 30, '13800138002'),
('王五', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjZjcOPk8xVEOkwJBbQbC9H7FOdHNe', 28, '13800138003');

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- 创建审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    request_path VARCHAR(255),
    request_method VARCHAR(10),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_data JSON,
    response_data JSON,
    status_code INT,
    duration_ms INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- 创建安全事件表
CREATE TABLE IF NOT EXISTS security_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    additional_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- 创建密码历史表
CREATE TABLE IF NOT EXISTS password_histories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- 更新用户表，添加密码策略相关字段
ALTER TABLE users 
ADD COLUMN password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN login_attempts INT DEFAULT 0,
ADD COLUMN locked_until TIMESTAMP NULL,
ADD COLUMN last_login_at TIMESTAMP NULL,
ADD COLUMN last_login_ip VARCHAR(45);

-- 创建索引优化查询性能
CREATE INDEX idx_audit_logs_composite ON audit_logs(user_id, created_at);
CREATE INDEX idx_audit_logs_status ON audit_logs(status_code);
CREATE INDEX idx_security_events_composite ON security_events(event_type, created_at);