package middleware

import (
	"html"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
)

// SecurityMiddleware 安全中间件
func SecurityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全头
		c.<PERSON><PERSON>("X-Content-Type-Options", "nosniff")
		c<PERSON><PERSON>("X-Frame-Options", "DENY")
		c<PERSON><PERSON><PERSON>("X-XSS-Protection", "1; mode=block")
		c<PERSON><PERSON><PERSON>("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.<PERSON><PERSON>("Content-Security-Policy", "default-src 'self'")
		
		c.Next()
	}
}

// XSSProtectionMiddleware XSS防护中间件
func XSSProtectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查查询参数
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsXSS(value) {
					c.<PERSON>(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": "检测到潜在的XSS攻击",
						"field":   key,
					})
					c.Abort()
					return
				}
			}
		}
		
		c.Next()
	}
}

// SQLInjectionProtectionMiddleware SQL注入防护中间件
func SQLInjectionProtectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查查询参数
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsSQLInjection(value) {
					c.JSON(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": "检测到潜在的SQL注入攻击",
						"field":   key,
					})
					c.Abort()
					return
				}
			}
		}
		
		c.Next()
	}
}

// containsXSS 检测XSS攻击
func containsXSS(input string) bool {
	xssPatterns := []string{
		`<script[^>]*>.*?</script>`,
		`javascript:`,
		`on\w+\s*=`,
		`<iframe[^>]*>`,
		`<object[^>]*>`,
		`<embed[^>]*>`,
		`<link[^>]*>`,
		`<meta[^>]*>`,
	}
	
	input = strings.ToLower(input)
	for _, pattern := range xssPatterns {
		matched, _ := regexp.MatchString(pattern, input)
		if matched {
			return true
		}
	}
	return false
}

// containsSQLInjection 检测SQL注入
func containsSQLInjection(input string) bool {
	sqlPatterns := []string{
		`'\s*(or|and)\s*'`,
		`'\s*(or|and)\s*\d+\s*=\s*\d+`,
		`union\s+select`,
		`drop\s+table`,
		`delete\s+from`,
		`insert\s+into`,
		`update\s+.*set`,
		`exec\s*\(`,
		`'\s*;\s*--`,
		`'\s*;\s*/\*`,
	}
	
	input = strings.ToLower(input)
	for _, pattern := range sqlPatterns {
		matched, _ := regexp.MatchString(pattern, input)
		if matched {
			return true
		}
	}
	return false
}

// SanitizeInput 清理输入
func SanitizeInput(input string) string {
	// HTML转义
	return html.EscapeString(input)
}