package middleware

import (
	"bytes"
	"io"
	"time"

	"go-project/database"
	"go-project/models"
	"go-project/utils"

	"github.com/gin-gonic/gin"
)

// responseWriter 包装响应写入器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// AuditLogMiddleware 审计日志中间件
func AuditLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器
		w := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = w

		c.Next()

		// 记录审计日志
		go func() {
			duration := time.Since(start).Milliseconds()

			// 获取用户ID
			var userID *uint
			if userIDValue, exists := c.Get("user_id"); exists {
				if uid, ok := userIDValue.(uint); ok {
					userID = &uid
				}
			}

			// 创建审计日志
			auditLog := &models.AuditLog{
				UserID:      userID,
				Action:      getActionFromPath(c.Request.Method, c.Request.URL.Path),
				Resource:    getResourceFromPath(c.Request.URL.Path),
				Method:      c.Request.Method,
				Path:        c.Request.URL.Path,
				IPAddress:   c.ClientIP(),
				UserAgent:   c.Request.UserAgent(),
				RequestData: string(requestBody),
				Response:    w.body.String(),
				StatusCode:  c.Writer.Status(),
				Duration:    duration,
			}

			// 保存到数据库
			db := database.GetDB()
			if err := db.Create(auditLog).Error; err != nil {
				utils.LogError("保存审计日志失败: " + err.Error())
			}
		}()
	}
}

// getActionFromPath 从路径获取操作类型
func getActionFromPath(method, path string) string {
	switch method {
	case "GET":
		return "查询"
	case "POST":
		return "创建"
	case "PUT":
		return "更新"
	case "DELETE":
		return "删除"
	default:
		return "其他"
	}
}

// getResourceFromPath 从路径获取资源类型
func getResourceFromPath(path string) string {
	if contains(path, "/users") {
		return "用户"
	}
	if contains(path, "/auth") {
		return "认证"
	}
	return "未知"
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				bytes.Contains([]byte(s), []byte(substr)))))
}
