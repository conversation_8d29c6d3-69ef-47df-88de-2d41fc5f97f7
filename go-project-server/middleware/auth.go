package middleware

import (
	"net/http"
	"strings"
	"time"

	"go-project/database"
	"go-project/models"
	"go-project/utils"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取 token
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "缺少认证令牌",
			})
			c.Abort()
			return
		}

		// 检查 Bearer 前缀
		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "认证令牌格式错误",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]

		// 解析 token
		claims, err := utils.ParseToken(tokenString)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效的认证令牌",
			})
			c.Abort()
			return
		}

		// 检查 token 是否在数据库中存在且未过期
		db := database.GetDB()
		var userToken models.UserToken
		tokenHash := utils.HashToken(tokenString)
		err = db.Where("token_hash = ? AND user_id = ? AND expires_at > ?",
			tokenHash, claims.UserID, time.Now()).First(&userToken).Error
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "认证令牌已过期或无效",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Next()
	}
}
