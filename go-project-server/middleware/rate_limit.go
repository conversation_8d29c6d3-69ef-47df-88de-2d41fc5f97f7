package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 限流器结构
type RateLimiter struct {
	visitors map[string]*Visitor
	mu       sync.RWMutex
	rate     time.Duration
	burst    int
}

// Visitor 访问者信息
type Visitor struct {
	lastSeen time.Time
	count    int
	window   time.Time
}

// NewRateLimiter 创建新的限流器
func NewRateLimiter(rate time.Duration, burst int) *RateLimiter {
	rl := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		burst:    burst,
	}
	
	// 定期清理过期的访问者
	go rl.cleanupVisitors()
	return rl
}

// RateLimitMiddleware 限流中间件
func (rl *RateLimiter) RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()
		
		if !rl.allow(ip) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    429,
				"message": "请求过于频繁，请稍后再试",
				"retry_after": int(rl.rate.Seconds()),
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// allow 检查是否允许请求
func (rl *RateLimiter) allow(ip string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	now := time.Now()
	v, exists := rl.visitors[ip]
	
	if !exists {
		rl.visitors[ip] = &Visitor{
			lastSeen: now,
			count:    1,
			window:   now,
		}
		return true
	}
	
	// 重置窗口
	if now.Sub(v.window) > rl.rate {
		v.count = 1
		v.window = now
		v.lastSeen = now
		return true
	}
	
	// 检查是否超过限制
	if v.count >= rl.burst {
		v.lastSeen = now
		return false
	}
	
	v.count++
	v.lastSeen = now
	return true
}

// cleanupVisitors 清理过期访问者
func (rl *RateLimiter) cleanupVisitors() {
	ticker := time.NewTicker(time.Minute)
	for {
		select {
		case <-ticker.C:
			rl.mu.Lock()
			for ip, v := range rl.visitors {
				if time.Since(v.lastSeen) > time.Hour {
					delete(rl.visitors, ip)
				}
			}
			rl.mu.Unlock()
		}
	}
}