package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
// 在User结构体中添加以下字段
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey" example:"1"`
	Name      string         `json:"name" gorm:"not null" example:"张三" validate:"required"`
	Email     string         `json:"email" gorm:"type:varchar(255);uniqueIndex;not null" example:"<EMAIL>" validate:"required,email"`
	Password  string         `json:"-" gorm:"not null" validate:"required,min=6"` // 密码不在JSON中返回
	Age       int            `json:"age" example:"25" validate:"min=1,max=120"`
	Phone     string         `json:"phone" example:"13800138000"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	PasswordChangedAt *time.Time `json:"-" gorm:"default:null"` // 密码修改时间
	LoginAttempts     int        `json:"-" gorm:"default:0"`    // 登录尝试次数
	LockedUntil       *time.Time `json:"-" gorm:"default:null"` // 锁定到期时间
	LastLoginAt       *time.Time `json:"last_login_at"`         // 最后登录时间
	LastLoginIP       string     `json:"-" gorm:"size:45"`      // 最后登录IP
}

// PasswordHistory 密码历史记录
type PasswordHistory struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	UserID       uint      `json:"user_id" gorm:"not null;index"`
	PasswordHash string    `json:"-" gorm:"not null;size:255"`
	CreatedAt    time.Time `json:"created_at"`
	User         User      `json:"-" gorm:"foreignKey:UserID"`
}

// UserToken 用户令牌模型
type UserToken struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null;index"`
	TokenHash string    `json:"token_hash" gorm:"size:255;not null;index"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null;index"`
	CreatedAt time.Time `json:"created_at"`
	User      User      `json:"user" gorm:"foreignKey:UserID"`
}

// RegisterRequest 用户注册请求
type RegisterRequest struct {
	Name     string `json:"name" example:"张三" validate:"required"`
	Email    string `json:"email" example:"<EMAIL>" validate:"required,email"`
	Password string `json:"password" example:"123456" validate:"required,min=6"`
	Age      int    `json:"age" example:"25" validate:"min=1,max=120"`
	Phone    string `json:"phone" example:"13800138000"`
}

// LoginRequest 登录请求结构体
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password string `json:"password" binding:"required,min=6" example:"123456"`
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
	Token string       `json:"token"`
	User  UserResponse `json:"user"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Name  string `json:"name" example:"张三" validate:"required"`
	Email string `json:"email" example:"<EMAIL>" validate:"required,email"`
	Age   int    `json:"age" example:"25" validate:"min=1,max=120"`
	Phone string `json:"phone" example:"13800138000"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Name  *string `json:"name,omitempty" example:"李四"`
	Email *string `json:"email,omitempty" example:"<EMAIL>"`
	Age   *int    `json:"age,omitempty" example:"30"`
	Phone *string `json:"phone,omitempty" example:"13900139000"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID        uint      `json:"id" example:"1"`
	Name      string    `json:"name" example:"张三"`
	Email     string    `json:"email" example:"<EMAIL>"`
	Age       int       `json:"age" example:"25"`
	Phone     string    `json:"phone" example:"13800138000"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
