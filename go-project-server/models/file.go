package models

import (
	"time"

	"gorm.io/gorm"
)

// File 文件模型
type File struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UserID       uint           `json:"user_id" gorm:"not null;index"` // 上传用户ID
	OriginalName string         `json:"original_name" gorm:"size:255;not null"` // 原始文件名
	FileName     string         `json:"file_name" gorm:"size:255;not null;uniqueIndex"` // 存储文件名
	FilePath     string         `json:"file_path" gorm:"size:500;not null"` // 文件路径
	FileSize     int64          `json:"file_size" gorm:"not null"` // 文件大小（字节）
	FileType     string         `json:"file_type" gorm:"size:100"` // 文件类型
	MimeType     string         `json:"mime_type" gorm:"size:100"` // MIME类型
	FileHash     string         `json:"file_hash" gorm:"size:64;index"` // 文件哈希值
	UploadType   string         `json:"upload_type" gorm:"size:20;default:'single'"` // 上传类型：single/chunk
	Status       string         `json:"status" gorm:"size:20;default:'completed'"` // 状态：uploading/completed/failed
	DownloadCount int           `json:"download_count" gorm:"default:0"` // 下载次数
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// FileUploadLog 文件上传日志
type FileUploadLog struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	FileID    uint      `json:"file_id" gorm:"not null;index"`
	UserID    uint      `json:"user_id" gorm:"not null;index"`
	Action    string    `json:"action" gorm:"size:50;not null"` // upload/download/delete
	IPAddress string    `json:"ip_address" gorm:"size:45"`
	UserAgent string    `json:"user_agent" gorm:"size:500"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联关系
	File File `json:"file" gorm:"foreignKey:FileID"`
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// FileShare 文件分享
type FileShare struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	FileID     uint           `json:"file_id" gorm:"not null;index"`
	UserID     uint           `json:"user_id" gorm:"not null;index"` // 分享者ID
	ShareCode  string         `json:"share_code" gorm:"size:32;uniqueIndex;not null"` // 分享码
	Password   string         `json:"-" gorm:"size:255"` // 分享密码（可选）
	ExpiresAt  *time.Time     `json:"expires_at"` // 过期时间（可选）
	DownloadLimit int         `json:"download_limit" gorm:"default:0"` // 下载次数限制（0为无限制）
	DownloadCount int         `json:"download_count" gorm:"default:0"` // 已下载次数
	IsActive   bool           `json:"is_active" gorm:"default:true"` // 是否激活
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	File File `json:"file" gorm:"foreignKey:FileID"`
	User User `json:"user" gorm:"foreignKey:UserID"`
}