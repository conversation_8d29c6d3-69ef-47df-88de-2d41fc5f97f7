package models

import (
	"time"
	"gorm.io/gorm"
)

// AuditLog 审计日志模型
type AuditLog struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      *uint          `json:"user_id" gorm:"index"` // 可为空，匿名操作
	Action      string         `json:"action" gorm:"not null;size:100"` // 操作类型
	Resource    string         `json:"resource" gorm:"not null;size:100"` // 资源类型
	ResourceID  *uint          `json:"resource_id"` // 资源ID
	Method      string         `json:"method" gorm:"not null;size:10"` // HTTP方法
	Path        string         `json:"path" gorm:"not null;size:255"` // 请求路径
	IPAddress   string         `json:"ip_address" gorm:"not null;size:45"` // IP地址
	UserAgent   string         `json:"user_agent" gorm:"size:500"` // 用户代理
	RequestData string         `json:"request_data" gorm:"type:text"` // 请求数据
	Response    string         `json:"response" gorm:"type:text"` // 响应数据
	StatusCode  int            `json:"status_code"` // 状态码
	Duration    int64          `json:"duration"` // 执行时间(毫秒)
	CreatedAt   time.Time      `json:"created_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	User        *User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// SecurityEvent 安全事件模型
type SecurityEvent struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	EventType   string         `json:"event_type" gorm:"not null;size:50"` // 事件类型
	Severity    string         `json:"severity" gorm:"not null;size:20"` // 严重程度
	Description string         `json:"description" gorm:"not null;size:500"` // 事件描述
	IPAddress   string         `json:"ip_address" gorm:"not null;size:45"` // IP地址
	UserAgent   string         `json:"user_agent" gorm:"size:500"` // 用户代理
	UserID      *uint          `json:"user_id" gorm:"index"` // 用户ID
	Metadata    string         `json:"metadata" gorm:"type:text"` // 额外元数据
	CreatedAt   time.Time      `json:"created_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	User        *User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// 在现有文件末尾添加以下内容

// AuditLogFilter 审计日志筛选条件
type AuditLogFilter struct {
	UserID    *uint      `json:"user_id"`
	Action    string     `json:"action"`
	Resource  string     `json:"resource"`
	Method    string     `json:"method"`
	IPAddress string     `json:"ip_address"`
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
}

// SecurityEventFilter 安全事件筛选条件
type SecurityEventFilter struct {
	EventType string `json:"event_type"`
	Severity  string `json:"severity"`
}

// AuditLogResponse 审计日志响应
type AuditLogResponse struct {
	ID          uint      `json:"id"`
	UserID      *uint     `json:"user_id"`
	UserName    string    `json:"user_name"`
	Action      string    `json:"action"`
	Resource    string    `json:"resource"`
	ResourceID  *uint     `json:"resource_id"`
	Method      string    `json:"method"`
	Path        string    `json:"path"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	StatusCode  int       `json:"status_code"`
	Duration    int64     `json:"duration"`
	CreatedAt   time.Time `json:"created_at"`
}

// AuditStats 审计统计信息
type AuditStats struct {
	TotalRequests    int64                    `json:"total_requests"`
	SuccessRequests  int64                    `json:"success_requests"`
	FailedRequests   int64                    `json:"failed_requests"`
	UniqueUsers      int64                    `json:"unique_users"`
	UniqueIPs        int64                    `json:"unique_ips"`
	TopActions       []ActionStat             `json:"top_actions"`
	TopResources     []ResourceStat           `json:"top_resources"`
	HourlyStats      []HourlyStat             `json:"hourly_stats"`
	StatusCodeStats  []StatusCodeStat         `json:"status_code_stats"`
}

// ActionStat 操作统计
type ActionStat struct {
	Action string `json:"action"`
	Count  int64  `json:"count"`
}

// ResourceStat 资源统计
type ResourceStat struct {
	Resource string `json:"resource"`
	Count    int64  `json:"count"`
}

// HourlyStat 小时统计
type HourlyStat struct {
	Hour  int   `json:"hour"`
	Count int64 `json:"count"`
}

// StatusCodeStat 状态码统计
type StatusCodeStat struct {
	StatusCode int   `json:"status_code"`
	Count      int64 `json:"count"`
}