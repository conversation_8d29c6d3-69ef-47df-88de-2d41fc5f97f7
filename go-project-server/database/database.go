package database

import (
	"fmt"
	"go-project/config"
	"go-project/models"
	"go-project/utils"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

// InitDatabase 初始化数据库连接
func InitDatabase(cfg *config.Config) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName,
	)

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	utils.LogInfo("数据库连接成功")

	// 在InitDatabase函数中更新自动迁移部分
	err = DB.AutoMigrate(
		&models.User{},
		&models.UserToken{},
		&models.AuditLog{},
		&models.SecurityEvent{},
		&models.PasswordHistory{},
		&models.File{},           // 添加文件模型
		&models.FileUploadLog{},  // 添加文件上传日志模型
		&models.FileShare{},      // 添加文件分享模型
	)
	if err != nil {
		return fmt.Errorf("数据库迁移失败: %v", err)
	}

	utils.LogInfo("数据库迁移完成")
	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}
