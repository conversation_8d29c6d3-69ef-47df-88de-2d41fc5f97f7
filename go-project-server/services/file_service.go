package services

import (
	"errors"
	"time"

	"go-project/models"
	"gorm.io/gorm"
)

type FileService struct {
	db *gorm.DB
}

func NewFileService(db *gorm.DB) *FileService {
	return &FileService{db: db}
}

// CreateFile 创建文件记录
func (fs *FileService) CreateFile(file *models.File) error {
	return fs.db.Create(file).Error
}

// GetFileByID 根据ID获取文件
func (fs *FileService) GetFileByID(id uint) (*models.File, error) {
	var file models.File
	err := fs.db.Preload("User").First(&file, id).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetFileByName 根据文件名获取文件
func (fs *FileService) GetFileByName(fileName string) (*models.File, error) {
	var file models.File
	err := fs.db.Preload("User").Where("file_name = ?", fileName).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetUserFiles 获取用户的文件列表
func (fs *FileService) GetUserFiles(userID uint, page, pageSize int) ([]models.File, int64, error) {
	var files []models.File
	var total int64
	
	query := fs.db.Where("user_id = ?", userID)
	
	// 获取总数
	err := query.Model(&models.File{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&files).Error
	if err != nil {
		return nil, 0, err
	}
	
	return files, total, nil
}

// UpdateFileDownloadCount 更新文件下载次数
func (fs *FileService) UpdateFileDownloadCount(fileID uint) error {
	return fs.db.Model(&models.File{}).Where("id = ?", fileID).UpdateColumn("download_count", gorm.Expr("download_count + ?", 1)).Error
}

// DeleteFile 删除文件记录
func (fs *FileService) DeleteFile(fileID uint, userID uint) error {
	// 检查文件是否属于该用户
	var file models.File
	err := fs.db.Where("id = ? AND user_id = ?", fileID, userID).First(&file).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("文件不存在或无权限删除")
		}
		return err
	}
	
	// 软删除
	return fs.db.Delete(&file).Error
}

// LogFileAction 记录文件操作日志
func (fs *FileService) LogFileAction(log *models.FileUploadLog) error {
	return fs.db.Create(log).Error
}

// GetFileStats 获取文件统计信息
func (fs *FileService) GetFileStats(userID uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 总文件数
	var totalFiles int64
	fs.db.Model(&models.File{}).Where("user_id = ?", userID).Count(&totalFiles)
	stats["total_files"] = totalFiles
	
	// 总文件大小
	var totalSize int64
	fs.db.Model(&models.File{}).Where("user_id = ?", userID).Select("COALESCE(SUM(file_size), 0)").Scan(&totalSize)
	stats["total_size"] = totalSize
	
	// 今日上传文件数
	var todayFiles int64
	today := time.Now().Format("2006-01-02")
	fs.db.Model(&models.File{}).Where("user_id = ? AND DATE(created_at) = ?", userID, today).Count(&todayFiles)
	stats["today_files"] = todayFiles
	
	// 文件类型分布
	var typeStats []struct {
		FileType string `json:"file_type"`
		Count    int64  `json:"count"`
	}
	fs.db.Model(&models.File{}).Where("user_id = ?", userID).Select("file_type, COUNT(*) as count").Group("file_type").Scan(&typeStats)
	stats["type_distribution"] = typeStats
	
	return stats, nil
}