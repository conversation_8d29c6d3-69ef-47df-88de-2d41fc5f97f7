package services

import (
	"go-project/database"
	"go-project/models"
	"time"

	"gorm.io/gorm"
)

type AuditService struct {
	db *gorm.DB
}

func NewAuditService() *AuditService {
	return &AuditService{
		db: database.GetDB(),
	}
}

// GetAuditLogs 获取审计日志列表
func (s *AuditService) GetAuditLogs(page, size int, filters *models.AuditLogFilter) ([]models.AuditLogResponse, int64, error) {
	var logs []models.AuditLog
	var total int64

	// 构建查询
	query := s.db.Model(&models.AuditLog{}).Preload("User")

	// 应用筛选条件
	if filters != nil {
		if filters.UserID != nil {
			query = query.Where("user_id = ?", *filters.UserID)
		}
		if filters.Action != "" {
			query = query.Where("action LIKE ?", "%"+filters.Action+"%")
		}
		if filters.Resource != "" {
			query = query.Where("resource LIKE ?", "%"+filters.Resource+"%")
		}
		if filters.Method != "" {
			query = query.Where("method = ?", filters.Method)
		}
		if filters.IPAddress != "" {
			query = query.Where("ip_address LIKE ?", "%"+filters.IPAddress+"%")
		}
		if filters.StartDate != nil {
			query = query.Where("created_at >= ?", *filters.StartDate)
		}
		if filters.EndDate != nil {
			query = query.Where("created_at <= ?", *filters.EndDate)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Order("created_at DESC").Offset(offset).Limit(size).Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	var responses []models.AuditLogResponse
	for _, log := range logs {
		response := models.AuditLogResponse{
			ID:         log.ID,
			UserID:     log.UserID,
			Action:     log.Action,
			Resource:   log.Resource,
			ResourceID: log.ResourceID,
			Method:     log.Method,
			Path:       log.Path,
			IPAddress:  log.IPAddress,
			UserAgent:  log.UserAgent,
			StatusCode: log.StatusCode,
			Duration:   log.Duration,
			CreatedAt:  log.CreatedAt,
		}
		if log.User != nil {
			response.UserName = log.User.Name
		} else {
			response.UserName = "匿名用户"
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// GetSecurityEvents 获取安全事件列表
func (s *AuditService) GetSecurityEvents(page, size int, filters *models.SecurityEventFilter) ([]models.SecurityEvent, int64, error) {
	var events []models.SecurityEvent
	var total int64

	query := s.db.Model(&models.SecurityEvent{}).Preload("User")

	if filters != nil {
		if filters.EventType != "" {
			query = query.Where("event_type = ?", filters.EventType)
		}
		if filters.Severity != "" {
			query = query.Where("severity = ?", filters.Severity)
		}
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * size
	if err := query.Order("created_at DESC").Offset(offset).Limit(size).Find(&events).Error; err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// GetAuditStats 获取审计统计信息
func (s *AuditService) GetAuditStats(days int) (*models.AuditStats, error) {
	startDate := time.Now().AddDate(0, 0, -days)

	stats := &models.AuditStats{}

	// 总请求数
	s.db.Model(&models.AuditLog{}).Where("created_at >= ?", startDate).Count(&stats.TotalRequests)

	// 成功请求数
	s.db.Model(&models.AuditLog{}).Where("created_at >= ? AND status_code >= 200 AND status_code < 400", startDate).Count(&stats.SuccessRequests)

	// 失败请求数
	s.db.Model(&models.AuditLog{}).Where("created_at >= ? AND status_code >= 400", startDate).Count(&stats.FailedRequests)

	// 唯一用户数
	s.db.Model(&models.AuditLog{}).Where("created_at >= ? AND user_id IS NOT NULL", startDate).Distinct("user_id").Count(&stats.UniqueUsers)

	// 唯一IP数
	s.db.Model(&models.AuditLog{}).Where("created_at >= ?", startDate).Distinct("ip_address").Count(&stats.UniqueIPs)

	// 热门操作
	s.db.Model(&models.AuditLog{}).Select("action, COUNT(*) as count").Where("created_at >= ?", startDate).Group("action").Order("count DESC").Limit(10).Scan(&stats.TopActions)

	// 热门资源
	s.db.Model(&models.AuditLog{}).Select("resource, COUNT(*) as count").Where("created_at >= ?", startDate).Group("resource").Order("count DESC").Limit(10).Scan(&stats.TopResources)

	// 小时统计
	s.db.Model(&models.AuditLog{}).Select("HOUR(created_at) as hour, COUNT(*) as count").Where("created_at >= ?", startDate).Group("HOUR(created_at)").Order("hour").Scan(&stats.HourlyStats)

	// 状态码统计
	s.db.Model(&models.AuditLog{}).Select("status_code, COUNT(*) as count").Where("created_at >= ?", startDate).Group("status_code").Order("count DESC").Scan(&stats.StatusCodeStats)

	return stats, nil
}

// ExportAuditLogs 导出审计日志（不分页）
func (s *AuditService) ExportAuditLogs(filters *models.AuditLogFilter) ([]models.AuditLogResponse, error) {
	var logs []models.AuditLog

	// 构建查询
	query := s.db.Model(&models.AuditLog{}).Preload("User")

	// 应用筛选条件
	if filters != nil {
		if filters.UserID != nil {
			query = query.Where("user_id = ?", *filters.UserID)
		}
		if filters.Action != "" {
			query = query.Where("action LIKE ?", "%"+filters.Action+"%")
		}
		if filters.Resource != "" {
			query = query.Where("resource LIKE ?", "%"+filters.Resource+"%")
		}
		if filters.Method != "" {
			query = query.Where("method = ?", filters.Method)
		}
		if filters.IPAddress != "" {
			query = query.Where("ip_address LIKE ?", "%"+filters.IPAddress+"%")
		}
		if filters.StartDate != nil {
			query = query.Where("created_at >= ?", *filters.StartDate)
		}
		if filters.EndDate != nil {
			query = query.Where("created_at <= ?", *filters.EndDate)
		}
	}

	// 按创建时间倒序排列，限制最大导出数量
	if err := query.Order("created_at DESC").Limit(10000).Find(&logs).Error; err != nil {
		return nil, err
	}

	// 转换为响应格式
	var responses []models.AuditLogResponse
	for _, log := range logs {
		response := models.AuditLogResponse{
			ID:         log.ID,
			UserID:     log.UserID,
			Action:     log.Action,
			Resource:   log.Resource,
			ResourceID: log.ResourceID,
			Method:     log.Method,
			Path:       log.Path,
			IPAddress:  log.IPAddress,
			UserAgent:  log.UserAgent,
			StatusCode: log.StatusCode,
			Duration:   log.Duration,
			CreatedAt:  log.CreatedAt,
		}

		if log.User != nil {
			response.UserName = log.User.Name
		} else {
			response.UserName = "匿名用户"
		}

		responses = append(responses, response)
	}

	return responses, nil
}