import { apiClient } from './index'
import type {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  RegisterRequest,
  LoginRequest,
  LoginResponse,
  ApiResponse,
  PagedResponse
} from '@/types/user'

export const userApi = {
  // 认证相关
  register(data: RegisterRequest): Promise<ApiResponse<User>> {
    return apiClient.post('/auth/register', data)
  },

  login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post('/auth/login', data)
  },

  // 用户管理
  getUsers(params?: { page?: number; size?: number; search?: string }): Promise<PagedResponse<User[]>> {
    return apiClient.get('/users', { params })
  },

  getUser(id: number): Promise<ApiResponse<User>> {
    return apiClient.get(`/users/${id}`)
  },

  createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.post('/users', data)
  },

  updateUser(id: number, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.put(`/users/${id}`, data)
  },

  deleteUser(id: number): Promise<ApiResponse> {
    return apiClient.delete(`/users/${id}`)
  }
}