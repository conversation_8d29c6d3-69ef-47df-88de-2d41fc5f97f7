import { apiClient } from './index'

export interface FileInfo {
  fileName: string
  fileSize: number
  fileUrl: string
  uploadAt: string
}

export interface ChunkInfo {
  chunkIndex: number
  totalChunks: number
  fileName: string
  fileHash: string
}

export interface UploadResponse {
  code: number
  message: string
  data?: FileInfo | ChunkInfo
}

export const uploadApi = {
  // 普通文件上传
  singleUpload(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiClient.post('/upload/single', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 分片上传
  chunkUpload(
    chunk: Blob,
    chunkIndex: number,
    totalChunks: number,
    fileName: string,
    fileHash: string
  ): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('chunk', chunk)
    formData.append('chunkIndex', chunkIndex.toString())
    formData.append('totalChunks', totalChunks.toString())
    formData.append('fileName', fileName)
    formData.append('fileHash', fileHash)
    
    return apiClient.post('/upload/chunk', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 合并分片
  mergeChunks(chunkInfo: ChunkInfo): Promise<UploadResponse> {
    return apiClient.post('/upload/merge', chunkInfo)
  },

  // 下载文件
  downloadFile(fileName: string): string {
    return `http://localhost:8080/api/upload/file/${fileName}`
  }
}