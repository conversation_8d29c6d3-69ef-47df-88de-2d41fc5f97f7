import request from '@/utils/request';
import type { AuditLog, AuditStats, SecurityEvent } from '@/types/audit';

export interface AuditLogParams {
  page?: number;
  size?: number;
  user_id?: number;
  action?: string;
  resource?: string;
  method?: string;
  ip_address?: string;
  start_date?: string;
  end_date?: string;
}

export interface SecurityEventParams {
  page?: number;
  size?: number;
  event_type?: string;
  severity?: string;
}

export interface StatsParams {
  days?: number;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  total?: number;
  page?: number;
  size?: number;
}

export const auditApi = {
  // 获取审计日志
  getLogs(params: AuditLogParams): Promise<ApiResponse<AuditLog[]>> {
    return request.get('/audit/logs', { params });
  },

  // 获取安全事件
  getSecurityEvents(params: SecurityEventParams): Promise<ApiResponse<SecurityEvent[]>> {
    return request.get('/audit/security-events', { params });
  },

  // 获取统计信息
  getStats(params: StatsParams): Promise<ApiResponse<AuditStats>> {
    return request.get('/audit/stats', { params });
  },

  // 导出审计日志
  exportLogs(params: AuditLogParams): Promise<Blob> {
    return request.get('/audit/export', { 
      params,
      responseType: 'blob'
    });
  },
};