<template>
  <div class="audit-logs">
    <a-card title="审计日志管理" :bordered="false">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input
              v-model:value="filters.action"
              placeholder="操作类型"
              allow-clear
            />
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="filters.method"
              placeholder="HTTP方法"
              allow-clear
              style="width: 100%"
            >
              <a-select-option value="GET">GET</a-select-option>
              <a-select-option value="POST">POST</a-select-option>
              <a-select-option value="PUT">PUT</a-select-option>
              <a-select-option value="DELETE">DELETE</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-input
              v-model:value="filters.ip_address"
              placeholder="IP地址"
              allow-clear
            />
          </a-col>
          <a-col :span="6">
            <a-range-picker
              v-model:value="dateRange"
              style="width: 100%"
              @change="onDateRangeChange"
            />
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="6">
            <a-input
              v-model:value="filters.resource"
              placeholder="资源类型"
              allow-clear
            />
          </a-col>
          <a-col :span="6">
            <a-input-number
              v-model:value="filters.user_id"
              placeholder="用户ID"
              style="width: 100%"
              :min="1"
            />
          </a-col>
          <a-col :span="12">
            <a-space>
              <a-button type="primary" @click="searchLogs">
                <SearchOutlined /> 搜索
              </a-button>
              <a-button @click="resetFilters">
                <ReloadOutlined /> 重置
              </a-button>
              <a-button @click="exportLogs">
                <DownloadOutlined /> 导出
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section" style="margin: 24px 0">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="总请求数"
              :value="stats.total_requests"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="成功请求"
              :value="stats.success_requests"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="失败请求"
              :value="stats.failed_requests"
              :value-style="{ color: '#f5222d' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="唯一用户"
              :value="stats.unique_users"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 日志表格 -->
      <a-table
        :columns="columns"
        :data-source="logs"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status_code'">
            <a-tag
              :color="getStatusColor(record.status_code)"
            >
              {{ record.status_code }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'method'">
            <a-tag
              :color="getMethodColor(record.method)"
            >
              {{ record.method }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'duration'">
            {{ record.duration }}ms
          </template>
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a @click="showLogDetail(record)">
              {{ record.action }}
            </a>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 日志详情模态框 -->
    <a-modal
      v-model:open="detailVisible"
      title="日志详情"
      :footer="null"
      width="800px"
    >
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="ID">{{ selectedLog?.id }}</a-descriptions-item>
        <a-descriptions-item label="用户">{{ selectedLog?.user_name }}</a-descriptions-item>
        <a-descriptions-item label="操作">{{ selectedLog?.action }}</a-descriptions-item>
        <a-descriptions-item label="资源">{{ selectedLog?.resource }}</a-descriptions-item>
        <a-descriptions-item label="方法">{{ selectedLog?.method }}</a-descriptions-item>
        <a-descriptions-item label="路径">{{ selectedLog?.path }}</a-descriptions-item>
        <a-descriptions-item label="IP地址">{{ selectedLog?.ip_address }}</a-descriptions-item>
        <a-descriptions-item label="状态码">{{ selectedLog?.status_code }}</a-descriptions-item>
        <a-descriptions-item label="耗时">{{ selectedLog?.duration }}ms</a-descriptions-item>
        <a-descriptions-item label="时间">{{ formatDate(selectedLog?.created_at) }}</a-descriptions-item>
        <a-descriptions-item label="User Agent" :span="2">
          {{ selectedLog?.user_agent }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';
import { auditApi } from '@/api/audit';
import type { AuditLog, AuditStats } from '@/types/audit';

// 响应式数据
const loading = ref(false);
const logs = ref<AuditLog[]>([]);
const stats = ref<AuditStats>({
  total_requests: 0,
  success_requests: 0,
  failed_requests: 0,
  unique_users: 0,
  unique_ips: 0,
  top_actions: [],
  top_resources: [],
  hourly_stats: [],
  status_code_stats: [],
});

const filters = reactive({
  action: '',
  method: '',
  resource: '',
  ip_address: '',
  user_id: undefined as number | undefined,
  start_date: '',
  end_date: '',
});

const dateRange = ref();
const detailVisible = ref(false);
const selectedLog = ref<AuditLog | null>(null);

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '用户',
    dataIndex: 'user_name',
    key: 'user_name',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
  },
  {
    title: '资源',
    dataIndex: 'resource',
    key: 'resource',
    width: 100,
  },
  {
    title: '方法',
    dataIndex: 'method',
    key: 'method',
    width: 80,
  },
  {
    title: '路径',
    dataIndex: 'path',
    key: 'path',
    width: 200,
    ellipsis: true,
  },
  {
    title: 'IP地址',
    dataIndex: 'ip_address',
    key: 'ip_address',
    width: 120,
  },
  {
    title: '状态码',
    dataIndex: 'status_code',
    key: 'status_code',
    width: 80,
  },
  {
    title: '耗时',
    dataIndex: 'duration',
    key: 'duration',
    width: 80,
  },
  {
    title: '时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160,
  },
];

// 方法
const loadLogs = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      ...filters,
    };
    
    const response = await auditApi.getLogs(params);
    logs.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    message.error('加载日志失败');
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  try {
    const response = await auditApi.getStats({ days: 7 });
    stats.value = response.data;
  } catch (error) {
    message.error('加载统计信息失败');
  }
};

const searchLogs = () => {
  pagination.current = 1;
  loadLogs();
};

const resetFilters = () => {
  Object.assign(filters, {
    action: '',
    method: '',
    resource: '',
    ip_address: '',
    user_id: undefined,
    start_date: '',
    end_date: '',
  });
  dateRange.value = undefined;
  searchLogs();
};

const onDateRangeChange = (dates: any) => {
  if (dates && dates.length === 2) {
    filters.start_date = dates[0].format('YYYY-MM-DD');
    filters.end_date = dates[1].format('YYYY-MM-DD');
  } else {
    filters.start_date = '';
    filters.end_date = '';
  }
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadLogs();
};

const showLogDetail = (log: AuditLog) => {
  selectedLog.value = log;
  detailVisible.value = true;
};

const exportLogs = async () => {
  try {
    message.loading('正在导出，请稍候...', 0);
    
    // 使用当前的筛选条件进行导出
    const exportParams = {
      ...filters,
      // 移除分页参数，导出所有数据
    };
    
    const blob = await auditApi.exportLogs(exportParams);
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // 生成文件名
    const now = new Date();
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
    link.download = `audit_logs_${timestamp}.csv`;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 清理URL对象
    window.URL.revokeObjectURL(url);
    
    message.destroy();
    message.success('导出成功');
  } catch (error) {
    message.destroy();
    message.error('导出失败');
    console.error('Export error:', error);
  }
};

const getStatusColor = (statusCode: number) => {
  if (statusCode >= 200 && statusCode < 300) return 'green';
  if (statusCode >= 300 && statusCode < 400) return 'blue';
  if (statusCode >= 400 && statusCode < 500) return 'orange';
  if (statusCode >= 500) return 'red';
  return 'default';
};

const getMethodColor = (method: string) => {
  const colors: Record<string, string> = {
    GET: 'blue',
    POST: 'green',
    PUT: 'orange',
    DELETE: 'red',
  };
  return colors[method] || 'default';
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  loadLogs();
  loadStats();
});
</script>

<style scoped>
.audit-logs {
  padding: 24px;
}

.filter-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.stats-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
}
</style>