<template>
  <div>
    <a-row :gutter="16" style="margin-bottom: 24px">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总用户数"
            :value="stats.totalUsers"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="今日新增"
            :value="stats.todayNew"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <UserAddOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="活跃用户"
            :value="stats.activeUsers"
            :value-style="{ color: '#cf1322' }"
          >
            <template #prefix>
              <TeamOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="系统状态"
            value="正常"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="16">
      <a-col :span="16">
        <a-card title="快速操作" style="margin-bottom: 24px">
          <a-space size="large">
            <a-button type="primary" @click="$router.push('/users/create')">
              <UserAddOutlined />
              创建用户
            </a-button>
            <a-button @click="$router.push('/users')">
              <UnorderedListOutlined />
              用户列表
            </a-button>
            <a-button @click="refreshData">
              <ReloadOutlined />
              刷新数据
            </a-button>
          </a-space>
        </a-card>
        
        <a-card title="最近活动">
          <a-list
            :data-source="recentActivities"
            :loading="loading"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.title"
                  :description="item.description"
                >
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      <component :is="item.icon" />
                    </a-avatar>
                  </template>
                </a-list-item-meta>
                <div>{{ item.time }}</div>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card title="系统信息">
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="系统版本">v1.0.0</a-descriptions-item>
            <a-descriptions-item label="运行时间">{{ uptime }}</a-descriptions-item>
            <a-descriptions-item label="服务器状态">
              <a-tag color="green">运行中</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="数据库状态">
              <a-tag color="green">连接正常</a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  UserOutlined,
  UserAddOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  UnorderedListOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const loading = ref(false)
const uptime = ref('2天 3小时 45分钟')

const stats = reactive({
  totalUsers: 1234,
  todayNew: 12,
  activeUsers: 856,
  systemStatus: '正常'
})

const recentActivities = ref([
  {
    title: '新用户注册',
    description: '用户 张三 完成注册',
    time: '2分钟前',
    icon: 'UserAddOutlined',
    color: '#1890ff'
  },
  {
    title: '用户信息更新',
    description: '用户 李四 更新了个人信息',
    time: '5分钟前',
    icon: 'UserOutlined',
    color: '#52c41a'
  },
  {
    title: '系统备份',
    description: '数据库备份完成',
    time: '1小时前',
    icon: 'CheckCircleOutlined',
    color: '#faad14'
  }
])

const refreshData = () => {
  loading.value = true
  // 模拟数据刷新
  setTimeout(() => {
    loading.value = false
    message.success('数据刷新成功')
  }, 1000)
}

onMounted(() => {
  // 初始化数据
  refreshData()
})
</script>