<template>
  <div class="file-upload-container">
    <a-card title="文件上传" class="upload-card">
      <div class="upload-area">
        <a-upload-dragger
          v-model:file-list="fileList"
          :before-upload="handleBeforeUpload"
          :show-upload-list="false"
          :multiple="false"
          class="upload-dragger"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传。大于100MB的文件将自动使用分片上传。
          </p>
        </a-upload-dragger>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <a-progress
          :percent="uploadProgress"
          :status="uploadStatus"
          :stroke-color="{
            '0%': '#108ee9',
            '100%': '#87d068',
          }"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>

      <!-- 上传历史 -->
      <div v-if="uploadHistory.length > 0" class="upload-history">
        <a-divider>上传历史</a-divider>
        <a-list
          :data-source="uploadHistory"
          item-layout="horizontal"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a @click="downloadFile(item)">下载</a>
                <a @click="removeFromHistory(item)">删除</a>
              </template>
              <a-list-item-meta>
                <template #title>
                  <span>{{ item.fileName }}</span>
                </template>
                <template #description>
                  <span>大小: {{ formatFileSize(item.fileSize) }} | 上传时间: {{ item.uploadAt }}</span>
                </template>
                <template #avatar>
                  <file-outlined />
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined, FileOutlined } from '@ant-design/icons-vue'
import { uploadApi, type FileInfo } from '@/api/upload'
import type { UploadFile } from 'ant-design-vue'
import CryptoJS from 'crypto-js'

const fileList = ref<UploadFile[]>([])
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref<'normal' | 'exception' | 'success'>('normal')
const progressText = ref('')
const uploadHistory = ref<FileInfo[]>([])

const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
const CHUNK_SIZE = 2 * 1024 * 1024 // 2MB

// 处理文件上传前的逻辑
const handleBeforeUpload = (file: UploadFile) => {
  if (!file) return false
  
  const rawFile = file as any
  if (rawFile.size > MAX_FILE_SIZE) {
    // 大文件分片上传
    handleChunkUpload(rawFile)
  } else {
    // 普通上传
    handleSingleUpload(rawFile)
  }
  
  return false // 阻止默认上传行为
}

// 普通文件上传
const handleSingleUpload = async (file: File) => {
  uploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = 'normal'
  progressText.value = '正在上传...'
  
  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 200)
    
    const response = await uploadApi.singleUpload(file)
    
    clearInterval(progressInterval)
    uploadProgress.value = 100
    uploadStatus.value = 'success'
    progressText.value = '上传完成！'
    
    if (response.code === 200 && response.data) {
      uploadHistory.value.unshift(response.data as FileInfo)
      message.success('文件上传成功！')
    } else {
      throw new Error(response.message)
    }
  } catch (error: any) {
    uploadStatus.value = 'exception'
    progressText.value = '上传失败'
    message.error('上传失败: ' + error.message)
  } finally {
    setTimeout(() => {
      uploading.value = false
      uploadProgress.value = 0
    }, 2000)
  }
}

// 分片上传
const handleChunkUpload = async (file: File) => {
  uploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = 'normal'
  progressText.value = '正在计算文件哈希...'
  
  try {
    // 计算文件哈希
    const fileHash = await calculateFileHash(file)
    
    // 计算分片数量
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    progressText.value = `开始分片上传，共 ${totalChunks} 个分片`
    
    // 上传所有分片
    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE
      const end = Math.min(start + CHUNK_SIZE, file.size)
      const chunk = file.slice(start, end)
      
      progressText.value = `正在上传分片 ${i + 1}/${totalChunks}`
      
      await uploadApi.chunkUpload(chunk, i, totalChunks, file.name, fileHash)
      
      uploadProgress.value = Math.round(((i + 1) / totalChunks) * 90)
    }
    
    // 合并分片
    progressText.value = '正在合并文件...'
    const mergeResponse = await uploadApi.mergeChunks({
      chunkIndex: 0,
      totalChunks,
      fileName: file.name,
      fileHash
    })
    
    uploadProgress.value = 100
    uploadStatus.value = 'success'
    progressText.value = '上传完成！'
    
    if (mergeResponse.code === 200 && mergeResponse.data) {
      uploadHistory.value.unshift(mergeResponse.data as FileInfo)
      message.success('文件上传成功！')
    } else {
      throw new Error(mergeResponse.message)
    }
  } catch (error: any) {
    uploadStatus.value = 'exception'
    progressText.value = '上传失败'
    message.error('上传失败: ' + error.message)
  } finally {
    setTimeout(() => {
      uploading.value = false
      uploadProgress.value = 0
    }, 2000)
  }
}

// 计算文件哈希
const calculateFileHash = (file: File): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const arrayBuffer = e.target?.result as ArrayBuffer
      const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer)
      const hash = CryptoJS.MD5(wordArray).toString()
      resolve(hash)
    }
    reader.readAsArrayBuffer(file)
  })
}

// 下载文件
const downloadFile = (fileInfo: FileInfo) => {
  const url = uploadApi.downloadFile(fileInfo.fileName)
  window.open(url, '_blank')
}

// 从历史记录中删除
const removeFromHistory = (fileInfo: FileInfo) => {
  const index = uploadHistory.value.findIndex(item => item.fileName === fileInfo.fileName)
  if (index > -1) {
    uploadHistory.value.splice(index, 1)
    message.success('已从历史记录中删除')
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-upload-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.upload-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-area {
  margin-bottom: 24px;
}

.upload-dragger {
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.3s;
}

.upload-dragger:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-progress {
  margin: 24px 0;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 6px;
}

.progress-text {
  margin-top: 8px;
  text-align: center;
  color: #666;
}

.upload-history {
  margin-top: 24px;
}
</style>