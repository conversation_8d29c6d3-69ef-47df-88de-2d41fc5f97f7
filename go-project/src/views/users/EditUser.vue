<template>
  <div>
    <a-page-header
      :title="`编辑用户 - ${user?.name || ''}`"
      @back="$router.go(-1)"
    />
    
    <a-card :loading="loading">
      <a-form
        v-if="user"
        :model="form"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
        @finish="handleSubmit"
      >
        <a-form-item label="姓名" name="name">
          <a-input
            v-model:value="form.name"
            placeholder="请输入用户姓名"
          />
        </a-form-item>
        
        <a-form-item label="邮箱" name="email">
          <a-input
            v-model:value="form.email"
            placeholder="请输入邮箱地址"
          />
        </a-form-item>
        
        <a-form-item label="年龄" name="age">
          <a-input-number
            v-model:value="form.age"
            placeholder="请输入年龄"
            :min="1"
            :max="120"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="电话" name="phone">
          <a-input
            v-model:value="form.phone"
            placeholder="请输入电话号码"
          />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 4, span: 14 }">
          <a-space>
            <a-button
              type="primary"
              html-type="submit"
              :loading="submitting"
            >
              保存更改
            </a-button>
            <a-button @click="resetForm">
              重置
            </a-button>
            <a-button @click="$router.go(-1)">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { userApi } from '@/api/user'
import type { User, UpdateUserRequest } from '@/types/user'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const submitting = ref(false)
const user = ref<User | null>(null)
const userId = Number(route.params.id)

const form = reactive<UpdateUserRequest>({
  name: '',
  email: '',
  age: 18,
  phone: ''
})

const rules = {
  name: [
    { required: true, message: '请输入用户姓名' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ],
  age: [
    { required: true, message: '请输入年龄' },
    { type: 'number', min: 1, max: 120, message: '年龄必须在1-120之间' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
  ]
}

// 获取用户详情
const fetchUser = async () => {
  try {
    loading.value = true
    const response = await userApi.getUser(userId)
    
    if (response.code === 200) {
      user.value = response.data
      // 填充表单
      Object.assign(form, {
        name: response.data.name,
        email: response.data.email,
        age: response.data.age,
        phone: response.data.phone
      })
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    message.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    submitting.value = true
    const response = await userApi.updateUser(userId, form)
    
    if (response.code === 200) {
      message.success('用户更新成功')
      router.push(`/users/${userId}`)
    }
  } catch (error) {
    console.error('更新用户失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (user.value) {
    Object.assign(form, {
      name: user.value.name,
      email: user.value.email,
      age: user.value.age,
      phone: user.value.phone
    })
  }
}

onMounted(() => {
  if (userId) {
    fetchUser()
  }
})
</script>