<template>
  <div>
    <a-page-header
      :title="user?.name || '用户详情'"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="$router.push(`/users/${userId}/edit`)">
            <EditOutlined />
            编辑用户
          </a-button>
          <a-popconfirm
            title="确定要删除这个用户吗？"
            @confirm="handleDelete"
            ok-text="确定"
            cancel-text="取消"
          >
            <a-button danger>
              <DeleteOutlined />
              删除用户
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </a-page-header>
    
    <a-card :loading="loading">
      <div v-if="user" class="user-detail">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="user-avatar">
              <a-avatar :size="120" class="avatar">
                {{ user.name.charAt(0) }}
              </a-avatar>
              <h3>{{ user.name }}</h3>
              <p class="user-email">{{ user.email }}</p>
            </div>
          </a-col>
          
          <a-col :span="16">
            <a-descriptions
              title="基本信息"
              :column="2"
              bordered
            >
              <a-descriptions-item label="用户ID">
                {{ user.id }}
              </a-descriptions-item>
              <a-descriptions-item label="姓名">
                {{ user.name }}
              </a-descriptions-item>
              <a-descriptions-item label="邮箱">
                <a-tag color="blue">{{ user.email }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="年龄">
                {{ user.age }} 岁
              </a-descriptions-item>
              <a-descriptions-item label="电话">
                {{ user.phone || '未填写' }}
              </a-descriptions-item>
              <a-descriptions-item label="注册时间">
                {{ formatDate(user.created_at) }}
              </a-descriptions-item>
              <a-descriptions-item label="更新时间">
                {{ formatDate(user.updated_at) }}
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag color="green">正常</a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>
      </div>
      
      <a-empty v-else-if="!loading" description="用户不存在" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { userApi } from '@/api/user'
import type { User } from '@/types/user'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const user = ref<User | null>(null)
const userId = Number(route.params.id)

// 获取用户详情
const fetchUser = async () => {
  try {
    loading.value = true
    const response = await userApi.getUser(userId)
    
    if (response.code === 200) {
      user.value = response.data
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    message.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 删除用户
const handleDelete = async () => {
  try {
    const response = await userApi.deleteUser(userId)
    if (response.code === 200) {
      message.success('删除成功')
      router.push('/users')
    }
  } catch (error) {
    console.error('删除用户失败:', error)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  if (userId) {
    fetchUser()
  }
})
</script>

<style scoped>
.user-detail {
  padding: 24px;
}

.user-avatar {
  text-align: center;
  padding: 24px;
}

.avatar {
  margin-bottom: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-size: 48px;
  font-weight: bold;
}

.user-avatar h3 {
  margin: 16px 0 8px 0;
  font-size: 24px;
  color: #1890ff;
}

.user-email {
  color: #666;
  font-size: 16px;
}
</style>