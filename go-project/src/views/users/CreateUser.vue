<template>
  <div>
    <a-page-header
      title="创建用户"
      sub-title="添加新的系统用户"
      @back="$router.go(-1)"
    />
    
    <a-card>
      <a-form
        :model="form"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
        @finish="handleSubmit"
      >
        <a-form-item label="姓名" name="name">
          <a-input
            v-model:value="form.name"
            placeholder="请输入用户姓名"
          />
        </a-form-item>
        
        <a-form-item label="邮箱" name="email">
          <a-input
            v-model:value="form.email"
            placeholder="请输入邮箱地址"
          />
        </a-form-item>
        
        <a-form-item label="年龄" name="age">
          <a-input-number
            v-model:value="form.age"
            placeholder="请输入年龄"
            :min="1"
            :max="120"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="电话" name="phone">
          <a-input
            v-model:value="form.phone"
            placeholder="请输入电话号码"
          />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 4, span: 14 }">
          <a-space>
            <a-button
              type="primary"
              html-type="submit"
              :loading="loading"
            >
              创建用户
            </a-button>
            <a-button @click="resetForm">
              重置
            </a-button>
            <a-button @click="$router.go(-1)">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { userApi } from '@/api/user'
import type { CreateUserRequest } from '@/types/user'

const router = useRouter()
const loading = ref(false)

const form = reactive<CreateUserRequest>({
  name: '',
  email: '',
  age: 18,
  phone: ''
})

const rules = {
  name: [
    { required: true, message: '请输入用户姓名' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ],
  age: [
    { required: true, message: '请输入年龄' },
    { type: 'number', min: 1, max: 120, message: '年龄必须在1-120之间' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true
    const response = await userApi.createUser(form)
    
    if (response.code === 201) {
      message.success('用户创建成功')
      router.push('/users')
    }
  } catch (error) {
    console.error('创建用户失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    email: '',
    age: 18,
    phone: ''
  })
}
</script>