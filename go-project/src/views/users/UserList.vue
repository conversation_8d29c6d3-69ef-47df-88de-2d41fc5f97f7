<template>
  <div>
    <div class="page-header">
      <div class="header-title">
        <h2>用户管理</h2>
        <p>管理系统中的所有用户</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="$router.push('/users/create')">
          <UserAddOutlined />
          创建用户
        </a-button>
      </div>
    </div>
    
    <a-card>
      <!-- 搜索和筛选 -->
      <div class="search-section">
        <a-row :gutter="16" align="middle">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchText"
              placeholder="搜索用户姓名或邮箱"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :span="4">
            <a-button @click="resetSearch">
              <ReloadOutlined />
              重置
            </a-button>
          </a-col>
        </a-row>
      </div>
      
      <!-- 用户表格 -->
      <a-table
        :columns="columns"
        :data-source="users"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a-avatar :size="32" style="margin-right: 8px">
              {{ record.name.charAt(0) }}
            </a-avatar>
            {{ record.name }}
          </template>
          
          <template v-else-if="column.key === 'email'">
            <a-tag color="blue">{{ record.email }}</a-tag>
          </template>
          
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button
                type="link"
                size="small"
                @click="viewUser(record.id)"
              >
                <EyeOutlined />
                查看
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="editUser(record.id)"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个用户吗？"
                @confirm="deleteUser(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button
                  type="link"
                  size="small"
                  danger
                >
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { userApi } from '@/api/user'
import type { User } from '@/types/user'
import {
  UserAddOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const loading = ref(false)
const searchText = ref('')
const users = ref<User[]>([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 250
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 100
  },
  {
    title: '电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    const response = await userApi.getUsers({
      page: pagination.current,
      size: pagination.pageSize,
      search: searchText.value
    })
    
    if (response.code === 200) {
      users.value = response.data || []
      pagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchUsers()
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  fetchUsers()
}

// 重置搜索
const resetSearch = () => {
  searchText.value = ''
  pagination.current = 1
  fetchUsers()
}

// 查看用户
const viewUser = (id: number) => {
  router.push(`/users/${id}`)
}

// 编辑用户
const editUser = (id: number) => {
  router.push(`/users/${id}/edit`)
}

// 删除用户
const deleteUser = async (id: number) => {
  try {
    const response = await userApi.deleteUser(id)
    if (response.code === 200) {
      message.success('删除成功')
      fetchUsers()
    }
  } catch (error) {
    console.error('删除用户失败:', error)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-title h2 {
  margin: 0;
  color: #1890ff;
}

.header-title p {
  margin: 4px 0 0 0;
  color: #666;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>