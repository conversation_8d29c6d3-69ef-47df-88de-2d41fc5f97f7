export default {
  app: {
    title: 'User Management System',
    shortTitle: 'UMS'
  },
  menu: {
    dashboard: 'Dashboard',
    users: 'User Management',
    upload: 'File Upload',
    auditLogs: 'Audit Logs'
  },
  header: {
    logout: 'Logout',
    logoutConfirm: 'Confirm Logout',
    logoutConfirmContent: 'Are you sure you want to log out?',
    theme: {
      light: 'Light Mode',
      dark: 'Dark Mode'
    },
    language: {
      zh: 'Chinese',
      en: 'English'
    }
  },
  breadcrumb: {
    dashboard: 'Dashboard',
    users: 'Users',
    createUser: 'Create User',
    userDetail: 'User Details',
    editUser: 'Edit User',
    upload: 'File Upload',
    auditLogs: 'Audit Logs'
  },
  footer: {
    copyright: 'User Management System ©{year}'
  },
  auditLogs: {
    title: 'Audit Log Management',
    filters: {
      action: 'Action Type',
      method: 'HTTP Method',
      resource: 'Resource Type',
      ipAddress: 'IP Address',
      dateRange: 'Date Range',
      search: 'Search',
      reset: 'Reset'
    },
    table: {
      time: 'Time',
      user: 'User',
      action: 'Action',
      resource: 'Resource',
      method: 'Method',
      path: 'Path',
      ip: 'IP Address',
      status: 'Status Code',
      duration: 'Duration(ms)',
      details: 'Details'
    },
    stats: {
      totalRequests: 'Total Requests',
      successRequests: 'Success Requests',
      failedRequests: 'Failed Requests',
      uniqueUsers: 'Unique Users',
      uniqueIps: 'Unique IPs'
    },
    modal: {
      title: 'Log Details',
      requestData: 'Request Data',
      response: 'Response Data',
      userAgent: 'User Agent'
    }
  }
}