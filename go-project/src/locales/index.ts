import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import enUS from './en-US'

const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 从localStorage获取保存的语言，如果没有则使用中文
const getLocale = () => {
  const savedLocale = localStorage.getItem('locale')
  return savedLocale || 'zh-CN'
}

export const i18n = createI18n({
  legacy: false, // 使用Composition API模式
  globalInjection: true, // 全局注入 $t 方法
  locale: getLocale(),
  fallbackLocale: 'zh-CN', // 备选语言
  messages
})

// 切换语言的方法
export const setLocale = (locale: 'zh-CN' | 'en-US') => {
  i18n.global.locale.value = locale
  localStorage.setItem('locale', locale)
  document.querySelector('html')?.setAttribute('lang', locale)
} 