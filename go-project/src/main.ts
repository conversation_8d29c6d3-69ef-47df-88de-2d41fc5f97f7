import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './style.css'
// 导入国际化
import { i18n } from './locales'

// 设置页面标题从环境变量读取
document.title = import.meta.env.VITE_APP_TITLE || 'Vite + Vue + TS'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(Antd)
app.use(i18n) // 使用国际化

app.mount('#app')
