import { ref, watch } from 'vue'

// 主题类型
export type Theme = 'light' | 'dark'

// 检查是否在浏览器环境
const isBrowser = typeof window !== 'undefined'

// 创建主题色管理组合函数
export default function useTheme() {
  // 从localStorage获取保存的主题，默认为浅色
  const getStoredTheme = (): Theme => {
    if (!isBrowser) return 'light'
    return localStorage.getItem('theme') as Theme || 'light'
  }

  const theme = ref<Theme>(getStoredTheme())

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }

  // 设置特定主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
  }

  // 应用主题色到CSS变量
  const applyTheme = (currentTheme: Theme) => {
    if (!isBrowser) return
    
    const root = document.documentElement
    
    // 添加过渡效果，避免突然的样式变化
    root.style.transition = 'all 0.3s ease'
    
    if (currentTheme === 'dark') {
      root.setAttribute('data-theme', 'dark')
    } else {
      root.setAttribute('data-theme', 'light')
    }
    
    // 移除过渡效果，避免影响其他动画
    setTimeout(() => {
      root.style.transition = ''
    }, 300)
  }

  // 监听主题变化，保存到localStorage并应用CSS变量
  watch(
    theme,
    (newTheme) => {
      if (isBrowser) {
        localStorage.setItem('theme', newTheme)
        applyTheme(newTheme)
      }
    },
    { immediate: true }
  )

  return {
    theme,
    toggleTheme,
    setTheme
  }
}