export interface User {
  id: number
  name: string
  email: string
  age: number
  phone: string
  created_at: string
  updated_at: string
}

export interface CreateUserRequest {
  name: string
  email: string
  age: number
  phone: string
}

export interface UpdateUserRequest {
  name?: string
  email?: string
  age?: number
  phone?: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  age: number
  phone: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

export interface PagedResponse<T = any> extends ApiResponse<T> {
  total: number
  page: number
  size: number
}