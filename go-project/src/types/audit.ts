export interface AuditLog {
  id: number;
  user_id?: number;
  user_name: string;
  action: string;
  resource: string;
  resource_id?: number;
  method: string;
  path: string;
  ip_address: string;
  user_agent: string;
  status_code: number;
  duration: number;
  created_at: string;
}

export interface SecurityEvent {
  id: number;
  event_type: string;
  severity: string;
  description: string;
  ip_address: string;
  user_agent: string;
  user_id?: number;
  metadata: string;
  created_at: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface AuditStats {
  total_requests: number;
  success_requests: number;
  failed_requests: number;
  unique_users: number;
  unique_ips: number;
  top_actions: ActionStat[];
  top_resources: ResourceStat[];
  hourly_stats: HourlyStat[];
  status_code_stats: StatusCodeStat[];
}

export interface ActionStat {
  action: string;
  count: number;
}

export interface ResourceStat {
  resource: string;
  count: number;
}

export interface HourlyStat {
  hour: number;
  count: number;
}

export interface StatusCodeStat {
  status_code: number;
  count: number;
}