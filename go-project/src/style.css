:root {
  /* 默认浅色主题变量 */
  --primary-color: #1890ff;
  --body-background: #f0f2f5;
  --component-background: #ffffff;
  --text-color: rgba(0, 0, 0, 0.85);
  --header-background: #ffffff;
}

[data-theme='dark'] {
  --primary-color: #1668dc;
  --body-background: #141414;
  --component-background: #1f1f1f;
  --text-color: rgba(255, 255, 255, 0.85);
  --header-background: #1f1f1f;
}

/* 防止横向滚动条 */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

#app {
  overflow-x: hidden;
  max-width: 100vw;
}

html {
  transition: all 0.3s;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: var(--body-background);
  color: var(--text-color);
  transition: all 0.3s;
}

#app {
  min-height: 100vh;
}

/* 自定义主题覆盖 */
[data-theme='dark'] .ant-layout-header {
  background-color: var(--header-background) !important;
}

[data-theme='dark'] .ant-layout-content .ant-layout-content-inner {
  background-color: var(--component-background) !important;
}

[data-theme='dark'] .ant-breadcrumb span,
[data-theme='dark'] .ant-breadcrumb a {
  color: rgba(255, 255, 255, 0.65) !important;
}
