<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider v-model:collapsed="collapsed" collapsible>
      <div class="logo">
        <h3 v-if="!collapsed" style="color: white; text-align: center; margin: 16px 0">
          {{ $t('app.title') }}
        </h3>
        <h3 v-else style="color: white; text-align: center; margin: 16px 0">
          {{ $t('app.shortTitle') }}
        </h3>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="dark"
        mode="inline"
        @click="handleMenuClick"
      >
        <a-menu-item key="dashboard">
          <DashboardOutlined />
          <span>{{ $t('menu.dashboard') }}</span>
        </a-menu-item>
        <a-menu-item key="users">
          <UserOutlined />
          <span>{{ $t('menu.users') }}</span>
        </a-menu-item>
        <a-menu-item key="upload">
          <UploadOutlined />
          <span>{{ $t('menu.upload') }}</span>
        </a-menu-item>
        <a-menu-item key="audit-logs">
          <FileTextOutlined />
          <span>{{ $t('menu.auditLogs') }}</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    
    <a-layout>
      <a-layout-header :style="{ background: 'var(--header-background)', padding: 0 }">
        <div class="header-content">
          <a-breadcrumb style="margin: 16px 24px">
            <a-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              <router-link v-if="item.path" :to="item.path">{{ item.name }}</router-link>
              <span v-else>{{ item.name }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
          
          <div class="header-actions">
            <!-- 主题切换 -->
            <a-tooltip>
              <template #title>
                {{ theme === 'light' ? $t('header.theme.dark') : $t('header.theme.light') }}
              </template>
              <a-button class="header-icon-btn" type="text" @click="toggleTheme">
                <template #icon>
                  <BulbOutlined v-if="theme === 'light'" />
                  <BulbFilled v-else />
                </template>
              </a-button>
            </a-tooltip>
            
            <!-- 语言切换 -->
            <a-dropdown>
              <a-button class="header-icon-btn" type="text">
                <GlobalOutlined />
              </a-button>
              <template #overlay>
                <a-menu @click="changeLanguage">
                  <a-menu-item key="zh-CN">{{ $t('header.language.zh') }}</a-menu-item>
                  <a-menu-item key="en-US">{{ $t('header.language.en') }}</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            
            <!-- 用户菜单 -->
            <a-dropdown>
              <a-button type="text">
                <UserOutlined />
                {{ authStore.user?.name }}
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="handleLogout">
                    <LogoutOutlined />
                    {{ $t('header.logout') }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>
      
      <a-layout-content style="margin: 24px 16px 0">
        <div class="content-container" :style="{ background: 'var(--component-background)' }">
          <router-view />
        </div>
      </a-layout-content>
      
      <a-layout-footer style="text-align: center">
        {{ $t('footer.copyright', { year: new Date().getFullYear() }) }}
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'
import useTheme from '@/composables/useTheme'
import {
  DashboardOutlined,
  UserOutlined,
  FileTextOutlined,
  UploadOutlined,
  DownOutlined,
  LogoutOutlined,
  GlobalOutlined,
  BulbOutlined,
  BulbFilled
} from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'
import { setLocale } from '@/locales'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const { t } = useI18n()
const { theme, toggleTheme } = useTheme()

const collapsed = ref(false)
const selectedKeys = ref<string[]>([])

// 面包屑导航
const breadcrumbs = computed(() => {
  const routeName = route.name as string
  const breadcrumbMap: Record<string, Array<{name: string, path?: string}>> = {
    Dashboard: [{ name: t('breadcrumb.dashboard') }],
    UserList: [{ name: t('breadcrumb.users'), path: '/users' }],
    CreateUser: [
      { name: t('breadcrumb.users'), path: '/users' },
      { name: t('breadcrumb.createUser') }
    ],
    UserDetail: [
      { name: t('breadcrumb.users'), path: '/users' },
      { name: t('breadcrumb.userDetail') }
    ],
    EditUser: [
      { name: t('breadcrumb.users'), path: '/users' },
      { name: t('breadcrumb.editUser') }
    ],
    FileUpload: [{ name: t('breadcrumb.upload') }],
    AuditLogs: [{ name: t('breadcrumb.auditLogs') }]
  }
  return breadcrumbMap[routeName] || []
})

// 监听路由变化更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    if (newPath.startsWith('/users')) {
      selectedKeys.value = ['users']
    } else if (newPath === '/dashboard') {
      selectedKeys.value = ['dashboard']
    } else if (newPath === '/upload') {
      selectedKeys.value = ['upload']
    } else if (newPath === '/audit-logs') {
      selectedKeys.value = ['audit-logs']
    }
  },
  { immediate: true }
)

// 菜单点击处理
const handleMenuClick = ({ key }: { key: string }) => {
  const routeMap: Record<string, string> = {
    dashboard: '/dashboard',
    users: '/users',
    upload: '/upload',
    'audit-logs': '/audit-logs'
  }
  
  if (routeMap[key]) {
    router.push(routeMap[key])
  }
}

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: t('header.logoutConfirm'),
    content: t('header.logoutConfirmContent'),
    onOk: () => {
      authStore.logout()
    }
  })
}

// 切换语言
const changeLanguage = ({ key }: { key: string }) => {
  setLocale(key as 'zh-CN' | 'en-US')
}
</script>

<style scoped>
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 防止布局溢出 */
.ant-layout {
  overflow-x: hidden;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  min-width: 0; /* 防止flex项目溢出 */
}

.content-container {
  padding: 24px;
  min-height: 360px;
  transition: all 0.3s;
  overflow-x: hidden; /* 防止内容溢出 */
}
</style>