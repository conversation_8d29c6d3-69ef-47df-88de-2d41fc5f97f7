// XSS防护
export function sanitizeInput(input: string): string {
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
}

// 验证输入
export function validateInput(input: string, type: 'email' | 'password' | 'text'): boolean {
  switch (type) {
    case 'email':
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    case 'password':
      return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(input);
    case 'text':
      return !/[<>"'&]/.test(input);
    default:
      return true;
  }
}

// 密码强度检查
export function checkPasswordStrength(password: string): {
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) score += 1;
  else feedback.push('密码长度至少8位');

  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('需要包含小写字母');

  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('需要包含大写字母');

  if (/\d/.test(password)) score += 1;
  else feedback.push('需要包含数字');

  if (/[@$!%*?&]/.test(password)) score += 1;
  else feedback.push('需要包含特殊字符');

  return { score, feedback };
}