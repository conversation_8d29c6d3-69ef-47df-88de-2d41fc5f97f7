<template>
  <a-config-provider :theme="themeConfig" :locale="antdLocale">
    <router-view />
  </a-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import useTheme from './composables/useTheme';
import { ConfigProvider } from 'ant-design-vue';
import { i18n } from './locales';
// 导入Ant Design Vue的locale
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import enUS from 'ant-design-vue/es/locale/en_US';

const { theme } = useTheme();

// 配置Ant Design Vue主题
const themeConfig = computed(() => {
  return {
    algorithm: theme.value === 'dark' 
      ? ConfigProvider.darkAlgorithm
      : ConfigProvider.defaultAlgorithm,
    token: {
      colorPrimary: theme.value === 'dark' ? '#1668dc' : '#1890ff',
    },
  };
});

// 根据当前语言动态设置Ant Design Vue的locale
const antdLocale = computed(() => {
  return i18n.global.locale.value === 'zh-CN' ? zhCN : enUS;
});
</script>

<style>
#app {
  min-height: 100vh;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
</style>