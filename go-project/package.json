{"name": "go-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "crypto-js": "^4.2.0", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}