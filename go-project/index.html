<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/go-project/src/assets/logo.webp" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loading...</title>
    <style>
      /* 防止页面加载时的布局闪烁 */
      html, body {
        overflow-x: hidden !important;
        max-width: 100vw;
      }
      
      /* 预设主题变量 */
      :root {
        --primary-color: #1890ff;
        --body-background: #f0f2f5;
        --component-background: #ffffff;
        --text-color: rgba(0, 0, 0, 0.85);
        --header-background: #ffffff;
      }
    </style>
    <script>
      // 在页面加载前应用保存的主题
      (function() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>