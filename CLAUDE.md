# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作提供指导。

## 项目架构概览

这是一个包含全栈 Web 应用的 monorepo：
- **前端**: Vue 3 + TypeScript 单页应用，位于 `/go-project/`
- **后端**: Go + Gin REST API，位于 `/go-project-server/`

## 环境配置

### 后端环境配置 (.env)
在 `/go-project-server/` 目录创建 `.env` 文件：
```env
PORT=8080
HOST=localhost
LOG_LEVEL=info

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=12345678
DB_NAME=go_project_db
```

### 前端环境配置 (.env)
在 `/go-project/` 目录创建 `.env` 文件：
```env
VITE_APP_TITLE=我的Vue应用
VITE_API_BASE_URL=http://localhost:8080
```

### 前端代理配置
前后端联调时，可在 `vite.config.ts` 中配置代理：
```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
    }
  }
}
```

## 开发命令

### 前端开发
```bash
cd go-project
npm install          # 安装依赖
npm run dev          # 启动开发服务器 (Vite) - 默认端口 5173
npm run build        # 生产环境构建
npm run preview      # 预览生产构建
```

### 后端开发
```bash
cd go-project-server
make deps            # 安装 Go 依赖
make swagger         # 生成 Swagger 文档
make build           # 编译应用 (输出到 bin/app.exe)
make run             # 运行应用 (默认端口 8080)
make dev             # 开发模式运行
make test            # 运行测试 (go test ./...)
make fmt             # 格式化 Go 代码
make vet             # 运行 go vet 代码检查
make db-init         # 初始化 MySQL 数据库
make clean           # 清理编译文件和文档
```

## 核心功能模块

### 1. 用户认证系统
- **JWT Token 认证**: 有效期 24 小时
- **密码加密**: 使用 bcrypt 哈希
- **相关文件**:
  - 后端: `/middleware/auth.go`, `/utils/jwt.go`, `/utils/password.go`
  - 前端: `/src/stores/auth.ts`, `/src/api/user.ts`

### 2. 文件上传功能
- **支持功能**:
  - 单文件上传
  - 大文件切片上传
  - 文件类型限制 (jpg, jpeg, png, gif, pdf, doc, docx, xls, xlsx)
  - 文件大小限制 (单文件 10MB，切片 5MB)
- **上传目录**: `/uploads/` (自动按日期分组)
- **相关文件**:
  - 后端: `/controllers/upload_controller.go`, `/services/file_service.go`
  - 前端: `/src/views/upload/FileUpload.vue`, `/src/api/upload.ts`

### 3. 审计日志系统
- **记录内容**: 所有用户操作（登录、创建、更新、删除）
- **自动记录**: 通过审计中间件自动记录
- **相关文件**:
  - 后端: `/middleware/audit.go`, `/services/audit_service.go`
  - 前端: `/src/views/AuditLogs.vue`, `/src/api/audit.ts`

### 4. 用户管理系统
- **功能**: 用户CRUD、角色权限管理
- **角色类型**: admin, user
- **相关文件**:
  - 后端: `/controllers/user_controller.go`, `/services/user_service.go`
  - 前端: `/src/views/users/` 目录下所有文件

## 核心架构模式

### 前端结构
- **API 层**: 所有 HTTP 请求通过 `/src/api/` 模块，使用带拦截器的 axios
- **状态管理**: Pinia stores 位于 `/src/stores/` - auth store 处理 JWT tokens
- **路由**: Vue Router 带认证守卫，路由定义在 `/src/router/`
- **国际化**: 支持中文(zh-CN)和英文(en-US)，语言文件在 `/src/locales/`
- **安全**: 请求加密/解密工具在 `/src/utils/security.ts`
- **主题**: 支持亮色/暗色主题切换，使用 `/src/composables/useTheme.ts`

### 后端结构
- **清洁架构**: Controllers → Services → Models/Database 分层
- **中间件栈**: 
  - CORS: 跨域请求处理
  - Security: 安全头设置
  - RateLimit: API 限流（每分钟 60 次）
  - Auth: JWT 认证
  - Audit: 操作审计
- **数据库**: MySQL + GORM ORM，迁移文件在 `/sql/migrations/`
- **日志**: 使用自定义 logger，支持分级日志

### 数据库架构
- **users**: 用户表（id, username, password, email, role, created_at, updated_at）
- **audit_logs**: 审计日志表（id, user_id, action, resource, details, ip_address, created_at）
- **files**: 文件表（id, user_id, filename, original_name, file_size, file_type, file_path, created_at）
- 运行 `make db-init` 创建初始架构

## 代码示例

### 添加新的 API 端点

1. **定义路由** (`/go-project-server/routes/routes.go`):
```go
// 在 SetupRoutes 函数中添加
api.POST("/example", middleware.AuthRequired(), exampleController.Create)
```

2. **创建控制器** (`/go-project-server/controllers/example_controller.go`):
```go
func (c *ExampleController) Create(ctx *gin.Context) {
    var req CreateExampleRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        ctx.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    result, err := c.exampleService.Create(&req)
    if err != nil {
        ctx.JSON(500, gin.H{"error": "创建失败"})
        return
    }
    
    ctx.JSON(200, gin.H{"data": result})
}
```

3. **前端 API 调用** (`/go-project/src/api/example.ts`):
```typescript
import request from '@/utils/request'

export function createExample(data: CreateExampleData) {
  return request({
    url: '/api/example',
    method: 'post',
    data
  })
}
```

### 使用中间件
```go
// 全局中间件
r.Use(middleware.CORS())
r.Use(middleware.Security())

// 路由组中间件
api := r.Group("/api")
api.Use(middleware.RateLimit())

// 特定路由中间件
api.GET("/protected", middleware.AuthRequired(), handler)
```

## 故障排查指南

### 常见问题

1. **前端无法连接后端**
   - 检查后端是否运行在 8080 端口
   - 检查前端代理配置是否正确
   - 查看浏览器控制台的 CORS 错误

2. **数据库连接失败**
   - 确认 MySQL 服务已启动
   - 检查 `.env` 中的数据库配置
   - 确认数据库 `go_project_db` 已创建

3. **JWT 认证失败**
   - 检查 token 是否过期（24小时有效期）
   - 确认请求头中包含 `Authorization: Bearer <token>`
   - 查看后端日志中的认证错误信息

### 日志查看
- **后端日志**: 直接在控制台查看，日志级别通过 `LOG_LEVEL` 环境变量控制
- **前端日志**: 浏览器开发者工具控制台
- **审计日志**: 通过管理界面查看用户操作历史

### 调试技巧
1. 使用 Swagger 文档测试 API: `http://localhost:8080/swagger/index.html`
2. 前端开启 Vue Devtools 查看组件状态
3. 后端使用 `fmt.Printf` 或 logger 输出调试信息
4. 使用 Postman 或 curl 直接测试 API 接口

## 部署指南

### 前端部署
```bash
cd go-project
npm run build
# 生成的 dist 目录可部署到任何静态文件服务器
```

### 后端部署
```bash
cd go-project-server
make build
# 运行编译后的二进制文件
./bin/app.exe
```

### 生产环境配置
1. 修改 `.env` 文件中的配置
2. 使用反向代理（如 Nginx）处理前端静态文件和后端 API
3. 配置 HTTPS 证书
4. 设置合适的 CORS 允许域名
5. 使用进程管理工具（如 systemd 或 supervisor）管理后端服务

## 安全最佳实践
- 生产环境必须修改默认的数据库密码
- 使用环境变量管理敏感配置，不要提交 `.env` 文件
- 定期更新依赖包以修复安全漏洞
- 启用 HTTPS 加密传输
- 实施 API 限流防止滥用
- 定期审查审计日志发现异常行为

## 重要文件位置
- 后端配置: `/go-project-server/config/config.go`
- 前端 API 配置: `/go-project/src/utils/request.ts`
- 认证中间件: `/go-project-server/middleware/auth.go`
- JWT 工具: `/go-project-server/utils/jwt.go`
- 路由定义: `/go-project-server/routes/routes.go`
- 前端路由: `/go-project/src/router/index.ts`
- 数据库初始化: `/go-project-server/sql/init.sql`