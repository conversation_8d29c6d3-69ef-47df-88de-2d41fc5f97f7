# Go-Project 全栈应用

这是一个基于 Vue 3 + TypeScript 前端和 Go + Gin 后端的全栈 Web 应用项目。

## 📁 项目结构

## 🚀 技术栈

### 前端 (go-project)

- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI 组件库**: Ant Design Vue
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP 客户端**: Axios
- **图标**: Ant Design Icons Vue

### 后端 (go-project-server)

- **语言**: Go 1.24.4
- **Web 框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL
- **认证**: JWT (golang-jwt/jwt)
- **API 文档**: Swagger
- **跨域处理**: gin-contrib/cors
- **环境配置**: godotenv
- **密码加密**: golang.org/x/crypto

## 📋 功能特性

- 🔐 用户认证与授权 (JWT)
- 👥 用户管理系统
- 📊 仪表板界面
- 🔄 RESTful API 设计
- 📖 Swagger API 文档
- 🛡️ 跨域请求处理
- 🎨 响应式 UI 设计
- 📱 移动端适配

## 🛠️ 开发环境搭建

### 前置要求

- Node.js >= 16.0.0
- Go >= 1.24.0
- MySQL >= 8.0

### 后端启动

1. 进入后端目录

```bash
cd go-project-server
```
